import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 加载数据
def load_data(file_path):
    try:
        # 尝试使用不同的引擎读取Excel文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, engine='openpyxl')
        elif file_path.endswith('.xls'):
            df = pd.read_excel(file_path, engine='xlrd')
        else:
            df = pd.read_excel(file_path)

        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        return df
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return None

# 统一列名
def unify_columns(df):
    # 创建列名映射字典
    column_mapping = {
        'spp': 'SPP', 'SPP': 'SPP', 'standpipe pressure': 'SPP', '立管压力': 'SPP', 'SPP立压': 'SPP',
        'tg': 'TG', 'TG': 'TG', 'total gas': 'TG', '总烃': 'TG', 'TG总烃': 'TG',
        'sum': 'Sum', 'Sum': 'Sum', 'pit volume total': 'Sum', '总池体积': 'Sum', 'PITTOTAL总池体积': 'Sum',
        'flow_out_percent': 'FlowOutPercent', 'FlowOutPercent': 'FlowOutPercent',
        'flow out percent': 'FlowOutPercent', '出口流量百分比': 'FlowOutPercent', 'FLOWOUTPERCENT出口流量百分比': 'FlowOutPercent',
        'label': 'Label', 'Label': 'Label', '标签': 'Label'
    }

    # 统一列名
    renamed_columns = {}
    for col in df.columns:
        col_lower = col.lower().strip()
        for key in column_mapping:
            if key.lower() in col_lower:
                renamed_columns[col] = column_mapping[key]
                break

    # 应用重命名
    df = df.rename(columns=renamed_columns)

    return df

# 查找溢流事件
def find_overflow_event(df):
    if 'Label' in df.columns:
        # 如果有标签列，查找标记为溢流的区域
        overflow_indices = np.where(df['Label'] == 1)[0]
        if len(overflow_indices) > 0:
            # 找到最长的连续溢流区域
            from itertools import groupby
            from operator import itemgetter

            # 找到所有连续的溢流区域
            consecutive_regions = []
            for k, g in groupby(enumerate(overflow_indices), lambda ix: ix[0] - ix[1]):
                consecutive_indices = list(map(itemgetter(1), g))
                consecutive_regions.append(consecutive_indices)

            # 选择最长的连续区域
            longest_region = max(consecutive_regions, key=len)

            # 找到这个区域的前后边界
            start_idx = max(0, longest_region[0] - 150)  # 溢流前150个点
            end_idx = min(len(df), longest_region[-1] + 150)  # 溢流后150个点

            # 确保总长度不超过300个点
            if end_idx - start_idx > 300:
                # 如果超过300点，保留溢流前100点和溢流后200点
                overflow_start = longest_region[0]
                start_idx = max(0, overflow_start - 100)
                end_idx = min(len(df), start_idx + 300)

            print(f"找到溢流区域: 从索引 {start_idx} 到 {end_idx}，溢流标记在索引 {longest_region[0]}")
            return df.iloc[start_idx:end_idx].copy(), longest_region[0] - start_idx

    # 如果没有标签列或没有找到溢流事件，尝试通过参数变化识别溢流
    if 'FlowOutPercent' in df.columns and 'TG' in df.columns:
        # 计算出口流量的变化率
        flow_change = df['FlowOutPercent'].diff().rolling(window=10).mean()

        # 找到出口流量显著增加的区域
        flow_threshold = flow_change.quantile(0.95)
        potential_overflow = flow_change > flow_threshold

        # 找到出口流量变化显著的点
        flow_indices = np.where(potential_overflow)[0]

        if len(flow_indices) > 0:
            # 找到第一个潜在溢流事件的开始
            overflow_idx = flow_indices[0]
            start_idx = max(0, overflow_idx - 100)
            end_idx = min(len(df), start_idx + 300)  # 限制为300个数据点
            print(f"通过参数变化识别到溢流区域: 从索引 {start_idx} 到 {end_idx}，潜在溢流点在索引 {overflow_idx}")
            return df.iloc[start_idx:end_idx].copy(), overflow_idx - start_idx

    # 如果都没找到，返回前300个数据点，并假设溢流点在中间
    print("未找到明确的溢流区域，返回前300个数据点")
    return df.iloc[:300].copy(), 150

# 归一化数据
def normalize_data(df, features):
    scaler = MinMaxScaler()
    df_norm = df.copy()
    df_norm[features] = scaler.fit_transform(df[features])
    return df_norm

# 主函数
def main():
    # 查找包含溢流的文件
    overflow_files = [
        os.path.join('数据集/test', '义斜162-钻进.xls')
    ]

    for file_path in overflow_files:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue

        # 加载数据
        df = load_data(file_path)
        if df is None:
            continue

        # 统一列名
        df = unify_columns(df)

        # 检查必要的列是否存在
        required_features = ['SPP', 'TG', 'Sum', 'FlowOutPercent']
        missing_features = [f for f in required_features if f not in df.columns]
        if missing_features:
            print(f"缺少必要的特征: {missing_features}")
            continue

        # 查找溢流事件
        event_df, overflow_idx = find_overflow_event(df)
        print(f"提取的事件数据形状: {event_df.shape}")

        # 归一化数据
        norm_df = normalize_data(event_df, required_features)

        # 绘制图表
        plt.figure(figsize=(12, 8))

        # 设置颜色
        colors = {
            'SPP': 'blue',
            'TG': 'green',
            'Sum': 'purple',
            'FlowOutPercent': 'red'
        }

        # 创建序列号作为x轴
        x_values = np.arange(len(norm_df))

        # 绘制参数变化曲线
        for feature in required_features:
            plt.plot(x_values, norm_df[feature],
                     label=feature, color=colors[feature], linewidth=2)

        # 标记溢流点 - 只标记一个点
        plt.axvline(x=overflow_idx, color='black', linestyle='--', linewidth=1.5)
        plt.annotate("溢流标记点", xy=(overflow_idx, 0.5), xytext=(overflow_idx+10, 0.7),
                    arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8),
                    fontsize=12, ha='center', va='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8, edgecolor='black'))

        # 添加标签
        plt.title("钻井溢流参数变化曲线", fontsize=16)
        plt.xlabel("序列号", fontsize=14)
        plt.ylabel("归一化参数值", fontsize=14)

        # 将图例放在右上角，并添加边框
        plt.legend(loc='upper right', fontsize=12, framealpha=0.9,
                  bbox_to_anchor=(0.99, 0.99), edgecolor='black')

        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()

        # 保存图表
        output_file = f"简化溢流参数变化曲线_{os.path.basename(file_path).split('.')[0]}.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图表已保存为: {output_file}")

        # 只处理第一个有效的文件
        break

if __name__ == "__main__":
    main()
