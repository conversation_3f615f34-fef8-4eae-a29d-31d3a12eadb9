import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建理想化的溢流参数响应时序数据
def create_ideal_response_data(n_points=300):
    # 创建时间轴（以秒为单位，假设采样间隔为10秒）
    sampling_interval = 10  # 秒
    time_axis = np.arange(n_points) * sampling_interval
    
    # 溢流开始时间点（第100个数据点）
    overflow_start_idx = 100
    overflow_start_time = time_axis[overflow_start_idx]
    
    # 创建参数响应数据
    data = {}
    
    # 1. 出口流量百分比 (FlowOutPercent) - 最快响应 (0-5分钟)
    flow_response_idx = overflow_start_idx + 3  # 30秒后响应
    flow_data = np.zeros(n_points)
    # 基线值
    flow_data[:overflow_start_idx] = 0.2
    # 溢流响应
    for i in range(flow_response_idx, n_points):
        # 快速上升，然后稳定在高值
        if i < flow_response_idx + 30:  # 5分钟内快速上升
            flow_data[i] = flow_data[flow_response_idx-1] + 0.025 * (i - flow_response_idx + 1)
        else:
            flow_data[i] = min(0.95, flow_data[i-1] + 0.001)
    # 添加一些噪声
    flow_data += np.random.normal(0, 0.01, n_points)
    data['FlowOutPercent'] = flow_data
    
    # 2. 立管压力 (SPP) - 次之响应 (5-10分钟)
    spp_response_idx = overflow_start_idx + 30  # 5分钟后响应
    spp_data = np.zeros(n_points)
    # 基线值
    spp_data[:overflow_start_idx] = 0.6
    # 溢流响应
    for i in range(overflow_start_idx, n_points):
        if i < spp_response_idx:  # 溢流开始到SPP响应之间保持稳定
            spp_data[i] = spp_data[overflow_start_idx-1]
        elif i < spp_response_idx + 60:  # 10分钟内缓慢下降
            spp_data[i] = spp_data[spp_response_idx-1] - 0.005 * (i - spp_response_idx + 1)
        else:
            spp_data[i] = max(0.3, spp_data[i-1] - 0.001)
    # 添加一些噪声
    spp_data += np.random.normal(0, 0.01, n_points)
    data['SPP'] = spp_data
    
    # 3. 总烃 (TG) - 滞后响应 (10-15分钟)
    tg_response_idx = overflow_start_idx + 60  # 10分钟后响应
    tg_data = np.zeros(n_points)
    # 基线值
    tg_data[:overflow_start_idx] = 0.1
    # 溢流响应
    for i in range(overflow_start_idx, n_points):
        if i < tg_response_idx:  # 溢流开始到TG响应之间保持稳定
            tg_data[i] = tg_data[overflow_start_idx-1]
        elif i < tg_response_idx + 30:  # 5分钟内快速上升
            tg_data[i] = tg_data[tg_response_idx-1] + 0.03 * (i - tg_response_idx + 1)
        else:
            tg_data[i] = min(0.9, tg_data[i-1] + 0.002)
    # 添加一些噪声
    tg_data += np.random.normal(0, 0.01, n_points)
    data['TG'] = tg_data
    
    # 4. 总池体积 (Sum) - 最慢响应 (15+分钟)
    sum_response_idx = overflow_start_idx + 90  # 15分钟后响应
    sum_data = np.zeros(n_points)
    # 基线值
    sum_data[:overflow_start_idx] = 0.4
    # 溢流响应
    for i in range(overflow_start_idx, n_points):
        if i < sum_response_idx:  # 溢流开始到Sum响应之间保持稳定
            sum_data[i] = sum_data[overflow_start_idx-1]
        else:  # 持续线性增加
            sum_data[i] = min(0.9, sum_data[i-1] + 0.004)
    # 添加一些噪声
    sum_data += np.random.normal(0, 0.005, n_points)
    data['Sum'] = sum_data
    
    # 记录响应时间点
    response_times = {
        'FlowOutPercent': flow_response_idx,
        'SPP': spp_response_idx,
        'TG': tg_response_idx,
        'Sum': sum_response_idx
    }
    
    return data, time_axis, response_times, overflow_start_idx

# 绘制理想化的溢流参数响应时序图表
def plot_ideal_response_chart(data, time_axis, response_times, overflow_start_idx):
    plt.figure(figsize=(14, 10))
    
    # 参数颜色和线型
    colors = {
        'FlowOutPercent': 'red',
        'SPP': 'blue',
        'TG': 'green',
        'Sum': 'purple'
    }
    
    line_styles = {
        'FlowOutPercent': ('-', 2.5),  # 线型, 线宽
        'SPP': ('-', 2.0),
        'TG': ('-', 2.0),
        'Sum': ('-', 2.0)
    }
    
    # 溢流开始时间
    overflow_start_time = time_axis[overflow_start_idx]
    
    # 绘制参数变化曲线
    for feature, values in data.items():
        plt.plot(time_axis, values, 
                 label=feature, 
                 color=colors[feature],
                 linestyle=line_styles[feature][0],
                 linewidth=line_styles[feature][1])
    
    # 标记溢流开始时间
    plt.axvline(x=overflow_start_time, color='black', linestyle='--', alpha=0.7, linewidth=1.5)
    plt.text(overflow_start_time, 0.05, "溢流开始", 
             color='black', ha='center', va='bottom', fontsize=10,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='black', boxstyle='round,pad=0.5'))
    
    # 标记响应时间点并添加时间标注
    for feature, time_idx in response_times.items():
        response_time = time_axis[time_idx]
        delay_minutes = (response_time - overflow_start_time) / 60  # 转换为分钟
        
        # 绘制垂直线标记响应时间点
        plt.axvline(x=response_time, color=colors[feature], linestyle='--', alpha=0.7, linewidth=1.5)
        
        # 添加响应时间标注
        y_pos = 0.95 if feature in ['FlowOutPercent', 'TG'] else 0.85
        plt.text(response_time, y_pos, 
                 f"{feature}\n响应时间: {int(delay_minutes)}分钟",
                 color=colors[feature], ha='center', va='bottom', fontsize=10,
                 bbox=dict(facecolor='white', alpha=0.7, edgecolor=colors[feature], boxstyle='round,pad=0.5'))
    
    # 添加参数响应时序说明
    response_order_text = (
        "钻井溢流监测参数响应时序:\n"
        "1. 出口流量(0-5分钟)\n"
        "2. 立管压力(5-10分钟)\n"
        "3. 总烃(10-15分钟)\n"
        "4. 总池体积(15+分钟)"
    )
    plt.text(0.02, 0.98, response_order_text, transform=plt.gca().transAxes,
             fontsize=12, verticalalignment='top', 
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    
    # 添加参数变化趋势说明
    trend_text = (
        "参数变化趋势:\n"
        "· 出口流量: 快速上升\n"
        "· 立管压力: 缓慢下降\n"
        "· 总烃: 滞后上升\n"
        "· 总池体积: 持续增加"
    )
    plt.text(0.98, 0.98, trend_text, transform=plt.gca().transAxes,
             fontsize=12, verticalalignment='top', horizontalalignment='right',
             bbox=dict(facecolor='white', alpha=0.8, edgecolor='gray', boxstyle='round,pad=0.5'))
    
    # 添加标签和图例
    plt.title("钻井溢流参数响应时序特性理想模型", fontsize=16)
    plt.xlabel("时间 (分钟)", fontsize=12)
    plt.ylabel("归一化参数值", fontsize=12)
    plt.legend(loc='upper center', fontsize=12, bbox_to_anchor=(0.5, -0.05), ncol=4)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 设置x轴为时间（分钟）
    plt.xticks(np.arange(0, max(time_axis) + 1, 300), 
               [f"{int(t/60)}" for t in np.arange(0, max(time_axis) + 1, 300)])
    
    # 添加溢流发生区域的阴影
    plt.axvspan(overflow_start_time, overflow_start_time + 300, 
                alpha=0.2, color='yellow', label='溢流发生初期')
    
    # 添加参数响应区域
    # 出口流量响应区域
    plt.axvspan(overflow_start_time, overflow_start_time + 300, 
                alpha=0.1, color=colors['FlowOutPercent'], label='出口流量响应区域')
    
    # 立管压力响应区域
    plt.axvspan(overflow_start_time + 300, overflow_start_time + 600, 
                alpha=0.1, color=colors['SPP'], label='立管压力响应区域')
    
    # 总烃响应区域
    plt.axvspan(overflow_start_time + 600, overflow_start_time + 900, 
                alpha=0.1, color=colors['TG'], label='总烃响应区域')
    
    # 总池体积响应区域
    plt.axvspan(overflow_start_time + 900, max(time_axis), 
                alpha=0.1, color=colors['Sum'], label='总池体积响应区域')
    
    # 保存图表
    plt.tight_layout()
    plt.savefig("钻井溢流参数响应时序特性理想模型.png", dpi=300, bbox_inches='tight')
    print("图表已保存为: 钻井溢流参数响应时序特性理想模型.png")
    
    # 显示图表
    plt.show()

# 主函数
def main():
    # 创建理想化的溢流参数响应时序数据
    data, time_axis, response_times, overflow_start_idx = create_ideal_response_data()
    
    # 绘制理想化的溢流参数响应时序图表
    plot_ideal_response_chart(data, time_axis, response_times, overflow_start_idx)

if __name__ == "__main__":
    main()
