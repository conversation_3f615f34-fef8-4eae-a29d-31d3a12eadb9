@echo off
echo 正在安装钻井数据分析与溢流预测系统所需依赖...
echo.

REM 检查Python是否已安装
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误：未找到Python，请先安装Python
    pause
    exit /b
)

echo 使用Python安装依赖项...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if %ERRORLEVEL% neq 0 (
    echo.
    echo 安装依赖项失败，请尝试手动安装：
    echo pip install torch numpy pandas matplotlib PyQt5 scikit-learn
    pause
    exit /b
)

echo.
echo 依赖安装完成！现在可以运行程序了。
echo 请运行: python app.py
pause
