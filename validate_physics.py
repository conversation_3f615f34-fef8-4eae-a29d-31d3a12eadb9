import torch
import numpy as np

def validate_physics(model, dataloader, df, used_features=None):
    """验证模型预测与物理约束的一致性"""
    if 'MaterialBalanceResidual' not in df.columns:
        print("缺少MaterialBalanceResidual列，无法验证物理约束")
        return
        
    model.eval()
    device = next(model.parameters()).device
    
    # 统计变量
    physics_violations = 0  # 物理规则违反数
    total_samples = 0       # 总样本数
    true_pos = 0            # 真正例（溢流样本正确预测为溢流）
    false_pos = 0           # 假正例（非溢流样本错误预测为溢流）
    true_neg = 0            # 真负例（非溢流样本正确预测为非溢流）
    false_neg = 0           # 假负例（溢流样本错误预测为非溢流）
    
    # 收集所有预测和残差值用于分析
    all_preds = []
    all_labels = []
    all_residuals = []
    
    with torch.no_grad():
        for i, (inputs, labels) in enumerate(dataloader):
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()
            
            outputs = model(inputs)
            preds = torch.argmax(outputs, dim=1).cpu().numpy()
            labels_np = labels.cpu().numpy()
            
            # 收集预测和真实标签
            all_preds.extend(preds)
            all_labels.extend(labels_np)
            
            # 处理每个样本
            batch_size = inputs.shape[0]
            seq_len = inputs.shape[1]
            
            # 尝试从测试集索引中获取对应的原始df索引
            try:
                # 如果有测试集索引，使用它们
                if hasattr(dataloader.dataset, 'indices'):
                    indices = dataloader.dataset.indices[i*batch_size:(i+1)*batch_size]
                else:
                    # 否则使用估计的索引
                    start_idx = i * dataloader.batch_size
                    indices = [start_idx + j + seq_len - 1 for j in range(batch_size)]
                    
                for j in range(batch_size):
                    if j >= len(indices):
                        continue
                        
                    df_idx = indices[j]
                    if df_idx >= len(df):
                        continue
                        
                    # 获取物质平衡残差
                    residual = df.iloc[df_idx]['MaterialBalanceResidual']
                    all_residuals.append(residual)
                    
                    # 统计物理规则违反
                    if preds[j] == 1 and residual < 0:
                        # 预测为溢流但残差为负，这是物理矛盾
                        physics_violations += 1
                        
                    # 统计混淆矩阵各类别
                    if preds[j] == 1 and labels_np[j] == 1:
                        true_pos += 1
                    elif preds[j] == 1 and labels_np[j] == 0:
                        false_pos += 1
                    elif preds[j] == 0 and labels_np[j] == 0:
                        true_neg += 1
                    elif preds[j] == 0 and labels_np[j] == 1:
                        false_neg += 1
                        
                    total_samples += 1
            except Exception as e:
                print(f"验证物理约束时出错: {str(e)}")
                continue
    
    # 计算统计指标
    physics_violation_rate = physics_violations / (total_samples + 1e-6)
    precision = true_pos / (true_pos + false_pos + 1e-6)
    recall = true_pos / (true_pos + false_neg + 1e-6)
    f1 = 2 * precision * recall / (precision + recall + 1e-6)
    
    # 打印统计结果
    print(f"物理规则违反比例: {physics_violation_rate:.2%}")
    print(f"混淆矩阵: TP={true_pos}, FP={false_pos}, TN={true_neg}, FN={false_neg}")
    print(f"精确率: {precision:.4f}, 召回率: {recall:.4f}, F1分数: {f1:.4f}")
    
    # 分析溢流预测与物质平衡残差的关系
    if len(all_preds) > 0 and len(all_residuals) > 0:
        overflow_residuals = [r for i, r in enumerate(all_residuals) if all_preds[i] == 1]
        normal_residuals = [r for i, r in enumerate(all_residuals) if all_preds[i] == 0]
        
        if len(overflow_residuals) > 0:
            print(f"溢流预测样本的残差统计: 平均值={np.mean(overflow_residuals):.4f}, 最小值={min(overflow_residuals):.4f}, 最大值={max(overflow_residuals):.4f}")
        if len(normal_residuals) > 0:
            print(f"非溢流预测样本的残差统计: 平均值={np.mean(normal_residuals):.4f}, 最小值={min(normal_residuals):.4f}, 最大值={max(normal_residuals):.4f}")
