import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from imblearn.over_sampling import SMOTE
from imblearn.over_sampling import BorderlineSMOTE
from handle_xls_file import handle_xls_file

def load_data(data_path):
    ext = os.path.splitext(data_path)[-1].lower()
    if ext == '.csv':
        df = pd.read_csv(data_path)
    elif ext in ['.xlsx', '.xls']:
        # 优先用openpyxl读取.xlsx，.xls建议转为.xlsx或.csv
        try:
            df = pd.read_excel(data_path, engine='openpyxl')
        except Exception as e:
            print(f"openpyxl读取失败: {e}")
            try:
                import xlrd
                df = pd.read_excel(data_path, engine='xlrd')
            except Exception as e2:
                print(f"xlrd读取失败: {e2}")
                raise
    else:
        raise ValueError("不支持的文件格式")
    return df

def smote_augment(df, feature_cols, label_col='Label'):
    X = df[feature_cols].values
    y = df[label_col].values
    unique, counts = np.unique(y, return_counts=True)
    class_counts = dict(zip(unique, counts))
    min_class_count = min(class_counts.values())
    k_neighbors = max(1, min(min_class_count - 1, 5))
    if min_class_count <= 1:
        print("警告：少数类样本数<=1，无法进行SMOTE增强，跳过增强。")
        return df.copy()
    print(f"Borderline-SMOTE增强参数: k_neighbors={k_neighbors}，少数类样本数={min_class_count}")
    smote = BorderlineSMOTE(random_state=42, k_neighbors=k_neighbors)
    X_res, y_res = smote.fit_resample(X, y)
    df_res = pd.DataFrame(X_res, columns=feature_cols)
    df_res[label_col] = y_res
    return df_res

def noise_augment(df, feature_cols, noise_std=0.8, random_seed=None, n_samples=1, mode='trend_preserve'):
    """
    对指定数值特征列添加高斯噪声，生成多样化增强样本
    mode:
      - 'trend_preserve': 对每列加一个全局随机偏移（整体平移），再加小幅点噪声，趋势基本不变但整体有变化
      - 'point': 仅加点噪声
    """
    dfs = [df.copy()]
    for i in range(n_samples):
        if random_seed is not None:
            np.random.seed(random_seed + i)
        df_noised = df.copy()
        for col in feature_cols:
            if np.issubdtype(df_noised[col].dtype, np.number):
                std = df_noised[col].std()
                if mode == 'trend_preserve':
                    # 整体加一个全局扰动（如均值偏移），再加小幅点噪声
                    global_shift = np.random.normal(0, noise_std * std)
                    point_noise = np.random.normal(0, 0.05 * std, size=len(df_noised))
                    df_noised[col] = df_noised[col] + global_shift + point_noise
                else:
                    # 仅加点噪声
                    noise = np.random.normal(0, noise_std * std, size=len(df_noised))
                    df_noised[col] = df_noised[col] + noise
        dfs.append(df_noised)
    return dfs

def sliding_window_sequences(df, feature_cols, window_size=20, label_col='Label'):
    X_seq, y_seq = [], []
    data = df[feature_cols].values
    labels = df[label_col].values
    for i in range(len(data) - window_size + 1):
        X_seq.append(data[i:i+window_size])
        y_seq.append(labels[i+window_size-1])
    return np.array(X_seq), np.array(y_seq)

def plot_compare(original, augmented, feature, title, ylabel):
    fig, axes = plt.subplots(2, 1, figsize=(8, 5), sharex=False)
    # 上图：原始数据
    axes[0].plot(original[feature].values, color='b')
    axes[0].set_title(f"{feature} 原始数据")
    axes[0].set_ylabel(ylabel)
    axes[0].grid(True, linestyle='--', alpha=0.5)
    # 下图：增强后数据
    axes[1].plot(augmented[feature].values, color='m', linestyle='--')
    axes[1].set_title(f"{feature} 增强后数据")
    axes[1].set_xlabel('时间')
    axes[1].set_ylabel(ylabel)
    axes[1].grid(True, linestyle='--', alpha=0.5)
    fig.suptitle(title)
    fig.tight_layout(rect=[0, 0, 1, 0.96])

def plot_noise_compare(original, noised, feature, title, ylabel):
    plt.figure(figsize=(8, 4))
    plt.plot(original[feature].values, color='b', label='原始数据')
    plt.plot(noised[feature].values, color='orange', linestyle='--', label='加噪声后数据')
    plt.title(title)
    plt.xlabel('时间')
    plt.ylabel(ylabel)
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    plt.tight_layout()

def plot_noise_compare_multi(original, noised_list, feature, title, ylabel):
    plt.figure(figsize=(8, 4))
    plt.plot(original[feature].values, color='b', label='原始数据')
    for idx, noised in enumerate(noised_list):
        plt.plot(noised[feature].values, linestyle='--', alpha=0.6, label=f'加噪声样本{idx+1}')
    plt.title(title)
    plt.xlabel('时间')
    plt.ylabel(ylabel)
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    plt.tight_layout()

def plot_window_demo(df, feature_cols, window_size=20, start_idx=0):
    plt.figure(figsize=(6,3))
    for i, col in enumerate(feature_cols):
        plt.plot(df[col].values[start_idx:start_idx+window_size], label=col)
    plt.title(f"{window_size}time steps滑动时间窗口")
    plt.xlabel('窗口内时间点')
    plt.ylabel('数值')
    plt.legend()
    plt.tight_layout()

def generate_labels(df):
    """
    根据溢流特征检测规则生成标签：
    1. FLOWOUTPERCENT出口流量百分比增大
    2. SPP立压下降
    3. ROP钻速加快
    4. Sum总池体积持续增大
    5. TG总烃持续增大（气侵特征）
    """
    df['Label'] = 0
    window = 10
    df['ROP_trend'] = df['FLOWOUTPERCENT出口流量百分比'].rolling(window).mean().diff() if 'FLOWOUTPERCENT出口流量百分比' in df.columns else 0
    df['SPP_trend'] = df['SPP立压'].rolling(window).mean().diff() if 'SPP立压' in df.columns else 0
    df['Sum_trend'] = df['PITTOTAL总池体积'].rolling(window).mean().diff() if 'PITTOTAL总池体积' in df.columns else 0

    # 兼容性处理
    if 'FLOWOUTPERCENT出口流量百分比' in df.columns:
        flow_percent_cond = df['FLOWOUTPERCENT出口流量百分比'].rolling(window).mean().diff() > 0.1
    else:
        flow_percent_cond = pd.Series(False, index=df.index)
    if 'SPP立压' in df.columns:
        spp_cond = df['SPP立压'].rolling(window).mean().diff() < -0.1
    else:
        spp_cond = pd.Series(False, index=df.index)
    if 'ROP钻速' in df.columns:
        rop_cond = df['ROP钻速'].rolling(window).mean().diff() > 0.2
    elif 'ROP' in df.columns:
        rop_cond = df['ROP'].rolling(window).mean().diff() > 0.2
    else:
        rop_cond = pd.Series(False, index=df.index)
    if 'PITTOTAL总池体积' in df.columns:
        sum_cond = df['PITTOTAL总池体积'].rolling(window).mean().diff() > 0.1
    else:
        sum_cond = pd.Series(False, index=df.index)
    if 'TG总烃' in df.columns:
        tg_cond = df['TG总烃'].rolling(window).mean().diff() > 0.1
    else:
        tg_cond = pd.Series(False, index=df.index)

    # 计算满足特征的数量 (不包括总烃)
    feature_conditions = [flow_percent_cond, spp_cond, rop_cond, sum_cond]
    condition_count = sum(cond.astype(int) for cond in feature_conditions)

    # 溢流条件1: 至少满足3个条件且必须包含出口流量百分比增大(首要特征)
    overflow_condition = (condition_count >= 3) & flow_percent_cond

    # 溢流条件2(气侵): 总烃增大且至少满足2个其他条件
    gas_invasion_condition = tg_cond & (condition_count >= 2)

    df.loc[(overflow_condition | gas_invasion_condition), 'Label'] = 1

    label_stats = df['Label'].value_counts()
    print(f"标签统计: 正常样本={label_stats.get(0, 0)}，溢流样本={label_stats.get(1, 0)}")
    return df

def plot_all_windows(X_seq, feature_cols, save_dir="window_samples_imgs"):
    """
    将每个滑动窗口样本单独画图并保存为图片。
    X_seq: shape (num_samples, window_size, num_features)
    feature_cols: 特征名列表
    """
    os.makedirs(save_dir, exist_ok=True)
    num_samples, window_size, num_features = X_seq.shape
    for i in range(num_samples):
        plt.figure(figsize=(6, 3))
        for j, col in enumerate(feature_cols):
            plt.plot(range(window_size), X_seq[i, :, j], label=col)
        plt.title(f"滑动窗口样本 {i+1}")
        plt.xlabel("窗口内时间点")
        plt.ylabel("数值")
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"window_{i+1:04d}.png"))
        plt.close()
    print(f"已保存所有滑动窗口样本图片到文件夹: {save_dir}")

def main():
    # 修改为你的数据路径
    data_path = r"C:\Users\<USER>\Desktop\毕设\模型\数据集\坨149-斜10-钻进3.xls"
    # 选取你关心的特征列
    feature_cols = ['FLOWOUTPERCENT出口流量百分比', 'SPP立压', 'PITTOTAL总池体积']
    label_col = 'Label'
    window_size = 30

    # 1. 加载数据
    df = handle_xls_file(data_path)
    print("原始数据:", df.shape)

    # 生成标签（采用Bi_LSTM的规则）
    df = generate_labels(df)

    # 2. SMOTE增强
    df_aug = smote_augment(df, feature_cols, label_col)
    print("增强后数据:", df_aug.shape)

    # 2.1 多样化噪声增强
    n_noise_samples = 3  # 生成3组不同的加噪声样本
    # 使用trend_preserve模式，整体扰动+小幅点噪声
    dfs_noised = noise_augment(df, feature_cols, noise_std=0.8, random_seed=42, n_samples=n_noise_samples, mode='trend_preserve')
    print(f"加噪声后数据组数: {len(dfs_noised)-1}")

    # 3. 滑动窗口法
    X_seq, y_seq = sliding_window_sequences(df_aug, feature_cols, window_size, label_col)
    print("滑动窗口样本数:", X_seq.shape[0])

    # 4. 绘图
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 原始与增强后数据对比
    for col in feature_cols:
        plot_compare(df, df_aug, col, f"{col}原始与Borderline-SMOTE增强后对比", col)
        plot_noise_compare_multi(df, dfs_noised[1:], col, f"{col}原始与多样化加噪声增强后对比", col)

    # 滑动窗口示意图
    plot_window_demo(df_aug, feature_cols, window_size, start_idx=0)

    # 展示所有图
    plt.show()

    # 5. 保存部分示意数据
    np.savez('window_samples_demo.npz', X_seq=X_seq, y_seq=y_seq)
    print("滑动窗口样本已保存为 window_samples_demo.npz")

    # 6. 保存每个滑动窗口样本图片
    plot_all_windows(X_seq, feature_cols)

if __name__ == "__main__":
    main()
