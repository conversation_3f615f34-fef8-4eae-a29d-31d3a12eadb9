#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型对比程序

该程序用于比较两个模型在同一测试文件上的溢流预测效果：
1. 基础模型：只使用四个基础特征（SPP、Sum、TG、FlowOutPercent）
2. 增强模型：添加了衍生特征及知识引导

功能：
1. 允许用户选择一个测试文件进行预测
2. 分别调用两个模型进行预测，确保数据预处理流程一致
3. 在同一图表中可视化对比两个模型的预测结果和真实标签
4. 计算并显示两个模型的性能指标（准确率、精确率、召回率、F1分数）
5. 突出显示两个模型预测结果不一致的区域，便于分析模型差异
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QGroupBox, QTextEdit,
                            QMessageBox, QSplitter, QFrame)
from PyQt5.QtCore import Qt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import joblib
import json
import traceback

# 导入基础模型和增强模型的相关函数
try:
    from Bi_LSTM import (
        DrillingModel as EnhancedModel,
        preprocess_features,
        calculate_constraint_features,
        create_sequences,
        unify_columns,
        calculate_DFO,
        handle_xls_file
    )
    from Bi_LSTM_basic import (
        DrillingModel as BasicModel,
        BASIC_FEATURES
    )
except ImportError:
    print("错误：无法导入模型模块。请确保Bi_LSTM.py和Bi_LSTM_Basic.py文件存在。")
    sys.exit(1)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class MatplotlibCanvas(FigureCanvas):
    """用于显示matplotlib图表的画布"""
    def __init__(self, parent=None, width=8, height=6, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)
        self.setParent(parent)
        self.fig.tight_layout()

class ModelComparisonApp(QMainWindow):
    """模型对比应用程序"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("钻井溢流预测模型对比工具")
        self.setGeometry(100, 100, 1200, 800)

        # 初始化变量
        self.test_file_path = None
        self.basic_model = None
        self.enhanced_model = None
        self.basic_scaler = None
        self.enhanced_scaler = None
        self.basic_features = None
        self.enhanced_features = None
        self.test_df = None
        self.prediction_results = None

        # 创建界面
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 创建主布局
        main_layout = QVBoxLayout(main_widget)

        # 创建上下分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)

        # 创建上部控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        splitter.addWidget(control_panel)

        # 创建文件选择区域
        file_group = QGroupBox("测试文件选择")
        file_layout = QHBoxLayout()
        file_group.setLayout(file_layout)

        self.file_path_text = QTextEdit()
        self.file_path_text.setMaximumHeight(50)
        self.file_path_text.setPlaceholderText("选择测试文件路径...")

        browse_file_btn = QPushButton("浏览...")
        browse_file_btn.clicked.connect(self.browse_test_file)

        load_file_btn = QPushButton("加载文件")
        load_file_btn.clicked.connect(self.load_test_file)

        file_layout.addWidget(self.file_path_text)
        file_layout.addWidget(browse_file_btn)
        file_layout.addWidget(load_file_btn)

        control_layout.addWidget(file_group)

        # 创建模型加载区域
        models_group = QGroupBox("模型加载")
        models_layout = QHBoxLayout()
        models_group.setLayout(models_layout)

        # 基础模型
        basic_model_layout = QVBoxLayout()
        basic_model_label = QLabel("基础模型 (4特征)")
        self.basic_model_path = QTextEdit()
        self.basic_model_path.setMaximumHeight(50)
        self.basic_model_path.setPlaceholderText("基础模型路径...")
        self.basic_model_path.setText("best_drilling_model_basic.pth")

        load_basic_btn = QPushButton("加载基础模型")
        load_basic_btn.clicked.connect(self.load_basic_model)

        basic_model_layout.addWidget(basic_model_label)
        basic_model_layout.addWidget(self.basic_model_path)
        basic_model_layout.addWidget(load_basic_btn)

        # 增强模型
        enhanced_model_layout = QVBoxLayout()
        enhanced_model_label = QLabel("增强模型 (含衍生特征)")
        self.enhanced_model_path = QTextEdit()
        self.enhanced_model_path.setMaximumHeight(50)
        self.enhanced_model_path.setPlaceholderText("增强模型路径...")
        self.enhanced_model_path.setText("best_drilling_model.pth")

        load_enhanced_btn = QPushButton("加载增强模型")
        load_enhanced_btn.clicked.connect(self.load_enhanced_model)

        enhanced_model_layout.addWidget(enhanced_model_label)
        enhanced_model_layout.addWidget(self.enhanced_model_path)
        enhanced_model_layout.addWidget(load_enhanced_btn)

        models_layout.addLayout(basic_model_layout)
        models_layout.addLayout(enhanced_model_layout)

        control_layout.addWidget(models_group)

        # 添加运行按钮
        run_btn = QPushButton("运行对比分析")
        run_btn.setMinimumHeight(40)
        run_btn.clicked.connect(self.run_comparison)
        control_layout.addWidget(run_btn)

        # 创建下部结果显示区域
        results_panel = QWidget()
        results_layout = QVBoxLayout(results_panel)
        splitter.addWidget(results_panel)

        # 创建结果分割器
        results_splitter = QSplitter(Qt.Horizontal)
        results_layout.addWidget(results_splitter)

        # 创建图表区域
        chart_frame = QFrame()
        chart_layout = QVBoxLayout(chart_frame)

        self.chart_canvas = MatplotlibCanvas(width=8, height=6)
        chart_layout.addWidget(self.chart_canvas)

        # 创建指标区域
        metrics_frame = QFrame()
        metrics_layout = QVBoxLayout(metrics_frame)

        self.metrics_text = QTextEdit()
        self.metrics_text.setReadOnly(True)
        metrics_layout.addWidget(self.metrics_text)

        # 添加到结果分割器
        results_splitter.addWidget(chart_frame)
        results_splitter.addWidget(metrics_frame)

        # 设置分割器比例
        splitter.setSizes([300, 500])
        results_splitter.setSizes([700, 300])

        # 设置状态栏
        self.statusBar().showMessage("就绪")

    def browse_test_file(self):
        """浏览测试文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择测试文件", "",
            "Excel文件 (*.xls *.xlsx);;CSV文件 (*.csv);;所有文件 (*.*)"
        )
        if file_path:
            self.file_path_text.setText(file_path)
            self.test_file_path = file_path
            self.statusBar().showMessage(f"已选择文件: {os.path.basename(file_path)}")

    def load_test_file(self):
        """加载测试文件"""
        file_path = self.file_path_text.toPlainText().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请先选择测试文件!")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
            return

        try:
            # 根据文件类型加载数据
            if file_path.endswith('.csv'):
                self.test_df = pd.read_csv(file_path)
            elif file_path.endswith('.xlsx'):
                self.test_df = pd.read_excel(file_path)
            elif file_path.endswith('.xls'):
                self.test_df = handle_xls_file(file_path)
            else:
                QMessageBox.warning(self, "警告", "不支持的文件格式!")
                return

            # 检查数据是否加载成功
            if self.test_df is None or len(self.test_df) == 0:
                QMessageBox.warning(self, "警告", "数据加载失败或文件为空!")
                return

            # 显示数据信息
            info_text = f"数据加载成功!\n"
            info_text += f"行数: {len(self.test_df)}\n"
            info_text += f"列数: {len(self.test_df.columns)}\n"

            # 检查是否有标签列
            if 'Label' in self.test_df.columns:
                label_counts = self.test_df['Label'].value_counts().to_dict()
                info_text += f"标签分布: {label_counts}\n"

            self.metrics_text.setText(info_text)
            self.statusBar().showMessage(f"数据加载成功: {len(self.test_df)}行 x {len(self.test_df.columns)}列")

        except Exception as e:
            error_msg = f"加载文件失败: {str(e)}\n{traceback.format_exc()}"
            self.metrics_text.setText(error_msg)
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")

    def load_basic_model(self):
        """加载基础模型"""
        model_path = self.basic_model_path.toPlainText().strip()
        if not model_path:
            model_path, _ = QFileDialog.getOpenFileName(
                self, "选择基础模型文件", "",
                "PyTorch模型 (*.pth);;所有文件 (*.*)"
            )
            if model_path:
                self.basic_model_path.setText(model_path)

        if not os.path.exists(model_path):
            QMessageBox.warning(self, "警告", f"模型文件不存在: {model_path}")
            return

        try:
            # 加载模型
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.basic_model = BasicModel(input_size=4).to(device)
            self.basic_model.load_state_dict(torch.load(model_path, map_location=device))
            self.basic_model.eval()

            # 加载标准化器
            scaler_path = os.path.join(os.path.dirname(model_path), "scaler_basic.pkl")
            if os.path.exists(scaler_path):
                self.basic_scaler = joblib.load(scaler_path)
            else:
                # 尝试加载默认名称的标准化器
                scaler_path = os.path.join(os.path.dirname(model_path), "scaler.pkl")
                if os.path.exists(scaler_path):
                    self.basic_scaler = joblib.load(scaler_path)
                else:
                    QMessageBox.warning(self, "警告", "未找到基础模型的标准化器文件!")

            # 加载特征列表
            features_path = os.path.join(os.path.dirname(model_path), "used_features_basic.json")
            if os.path.exists(features_path):
                with open(features_path, 'r') as f:
                    self.basic_features = json.load(f)
            else:
                # 使用默认基础特征
                self.basic_features = BASIC_FEATURES

            self.statusBar().showMessage(f"基础模型加载成功: {os.path.basename(model_path)}")

        except Exception as e:
            error_msg = f"加载基础模型失败: {str(e)}\n{traceback.format_exc()}"
            self.metrics_text.setText(error_msg)
            QMessageBox.critical(self, "错误", f"加载基础模型失败: {str(e)}")

    def load_enhanced_model(self):
        """加载增强模型"""
        model_path = self.enhanced_model_path.toPlainText().strip()
        if not model_path:
            model_path, _ = QFileDialog.getOpenFileName(
                self, "选择增强模型文件", "",
                "PyTorch模型 (*.pth);;所有文件 (*.*)"
            )
            if model_path:
                self.enhanced_model_path.setText(model_path)

        if not os.path.exists(model_path):
            QMessageBox.warning(self, "警告", f"模型文件不存在: {model_path}")
            return

        try:
            # 加载模型
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            # 从Bi_LSTM.py导入FINAL_FEATURES
            from Bi_LSTM import FINAL_FEATURES
            self.enhanced_model = EnhancedModel(input_size=len(FINAL_FEATURES)).to(device)
            self.enhanced_model.load_state_dict(torch.load(model_path, map_location=device))
            self.enhanced_model.eval()

            # 加载标准化器
            scaler_path = os.path.join(os.path.dirname(model_path), "scaler.pkl")
            if os.path.exists(scaler_path):
                self.enhanced_scaler = joblib.load(scaler_path)
            else:
                QMessageBox.warning(self, "警告", "未找到增强模型的标准化器文件!")

            # 加载特征列表
            features_path = os.path.join(os.path.dirname(model_path), "used_features.json")
            if os.path.exists(features_path):
                with open(features_path, 'r') as f:
                    self.enhanced_features = json.load(f)
            else:
                # 使用默认增强特征
                self.enhanced_features = FINAL_FEATURES

            self.statusBar().showMessage(f"增强模型加载成功: {os.path.basename(model_path)}")

        except Exception as e:
            error_msg = f"加载增强模型失败: {str(e)}\n{traceback.format_exc()}"
            self.metrics_text.setText(error_msg)
            QMessageBox.critical(self, "错误", f"加载增强模型失败: {str(e)}")

    def run_comparison(self):
        """运行模型对比分析"""
        # 检查是否已加载所有必要数据
        if self.test_df is None:
            QMessageBox.warning(self, "警告", "请先加载测试文件!")
            return

        if self.basic_model is None:
            QMessageBox.warning(self, "警告", "请先加载基础模型!")
            return

        if self.enhanced_model is None:
            QMessageBox.warning(self, "警告", "请先加载增强模型!")
            return

        if self.basic_scaler is None or self.enhanced_scaler is None:
            QMessageBox.warning(self, "警告", "标准化器未加载，预测结果可能不准确!")

        try:
            # 预处理数据
            self.statusBar().showMessage("正在预处理数据...")

            # 复制数据，避免修改原始数据
            df = self.test_df.copy()

            # 统一列名处理
            df = unify_columns(df)

            # 生成必要的特征
            df = calculate_DFO(df)
            df = preprocess_features(df)

            # 计算约束特征（用于增强模型）
            df_enhanced = calculate_constraint_features(df.copy())

            # 确保所有必需的特征都存在
            for feature in self.basic_features:
                if feature not in df.columns:
                    df[feature] = 0
                    print(f"警告: 添加缺失的基础特征: {feature}")

            for feature in self.enhanced_features:
                if feature not in df_enhanced.columns:
                    df_enhanced[feature] = 0
                    print(f"警告: 添加缺失的增强特征: {feature}")

            # 提取标签（如果存在）
            if 'Label' in df.columns:
                labels = df['Label'].values
                has_labels = True
            else:
                labels = np.zeros(len(df))
                has_labels = False

            # 标准化特征
            self.statusBar().showMessage("正在标准化特征...")

            # 基础模型特征标准化
            basic_features_df = df[self.basic_features].copy()
            if self.basic_scaler:
                basic_features_scaled = self.basic_scaler.transform(basic_features_df)
                basic_features_scaled_df = pd.DataFrame(basic_features_scaled, columns=self.basic_features)
            else:
                # 如果没有标准化器，使用原始特征
                basic_features_scaled_df = basic_features_df

            # 增强模型特征标准化
            enhanced_features_df = df_enhanced[self.enhanced_features].copy()
            if self.enhanced_scaler:
                enhanced_features_scaled = self.enhanced_scaler.transform(enhanced_features_df)
                enhanced_features_scaled_df = pd.DataFrame(enhanced_features_scaled, columns=self.enhanced_features)
            else:
                # 如果没有标准化器，使用原始特征
                enhanced_features_scaled_df = enhanced_features_df

            # 创建序列数据
            self.statusBar().showMessage("正在创建序列数据...")
            sequence_length = 30  # 默认序列长度

            # 基础模型序列
            X_basic, y_basic, _ = create_sequences(basic_features_scaled_df, labels, sequence_length=sequence_length)

            # 增强模型序列
            X_enhanced, y_enhanced, _ = create_sequences(enhanced_features_scaled_df, labels, sequence_length=sequence_length)

            # 检查序列数据是否为空
            if len(X_basic) == 0 or len(X_enhanced) == 0:
                QMessageBox.warning(self, "警告", f"数据点不足 ({len(df)}) 以构建长度为 {sequence_length} 的序列!")
                return

            # 进行预测
            self.statusBar().showMessage("正在进行预测...")
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

            # 基础模型预测
            basic_probs = []
            basic_preds = []

            with torch.no_grad():
                for i in range(0, len(X_basic), 64):  # 批次大小为64
                    batch = torch.FloatTensor(X_basic[i:i+64]).to(device)
                    outputs = self.basic_model(batch)
                    probs = torch.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs, 1)

                    basic_probs.extend(probs[:, 1].cpu().numpy())
                    basic_preds.extend(predicted.cpu().numpy())

            # 增强模型预测
            enhanced_probs = []
            enhanced_preds = []

            with torch.no_grad():
                for i in range(0, len(X_enhanced), 64):  # 批次大小为64
                    batch = torch.FloatTensor(X_enhanced[i:i+64]).to(device)
                    outputs = self.enhanced_model(batch)
                    probs = torch.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs, 1)

                    enhanced_probs.extend(probs[:, 1].cpu().numpy())
                    enhanced_preds.extend(predicted.cpu().numpy())

            # 创建结果DataFrame
            self.prediction_results = pd.DataFrame({
                'Index': range(len(basic_probs)),
                'Basic_Probability': basic_probs,
                'Basic_Prediction': basic_preds,
                'Enhanced_Probability': enhanced_probs,
                'Enhanced_Prediction': enhanced_preds,
                'Difference': np.abs(np.array(enhanced_probs) - np.array(basic_probs)),
                'Disagreement': np.array(enhanced_preds) != np.array(basic_preds)
            })

            # 如果有标签，添加到结果中
            if has_labels:
                self.prediction_results['True_Label'] = y_basic

            # 计算性能指标
            self.statusBar().showMessage("正在计算性能指标...")
            metrics_text = "模型性能对比:\n\n"

            if has_labels:
                # 基础模型指标
                basic_acc = accuracy_score(y_basic, basic_preds)
                basic_prec = precision_score(y_basic, basic_preds, zero_division=0)
                basic_rec = recall_score(y_basic, basic_preds, zero_division=0)
                basic_f1 = f1_score(y_basic, basic_preds, zero_division=0)

                # 增强模型指标
                enhanced_acc = accuracy_score(y_enhanced, enhanced_preds)
                enhanced_prec = precision_score(y_enhanced, enhanced_preds, zero_division=0)
                enhanced_rec = recall_score(y_enhanced, enhanced_preds, zero_division=0)
                enhanced_f1 = f1_score(y_enhanced, enhanced_preds, zero_division=0)

                # 添加到文本
                metrics_text += "基础模型 (4特征):\n"
                metrics_text += f"准确率: {basic_acc:.4f}\n"
                metrics_text += f"精确率: {basic_prec:.4f}\n"
                metrics_text += f"召回率: {basic_rec:.4f}\n"
                metrics_text += f"F1分数: {basic_f1:.4f}\n\n"

                metrics_text += "增强模型 (包含衍生特征):\n"
                metrics_text += f"准确率: {enhanced_acc:.4f}\n"
                metrics_text += f"精确率: {enhanced_prec:.4f}\n"
                metrics_text += f"召回率: {enhanced_rec:.4f}\n"
                metrics_text += f"F1分数: {enhanced_f1:.4f}\n\n"

                # 计算性能提升
                acc_improvement = (enhanced_acc - basic_acc) / basic_acc * 100 if basic_acc > 0 else float('inf')
                f1_improvement = (enhanced_f1 - basic_f1) / basic_f1 * 100 if basic_f1 > 0 else float('inf')

                metrics_text += "性能提升:\n"
                metrics_text += f"准确率提升: {acc_improvement:.2f}%\n"
                metrics_text += f"F1分数提升: {f1_improvement:.2f}%\n\n"

            # 计算预测差异
            disagreement_count = self.prediction_results['Disagreement'].sum()
            disagreement_percent = disagreement_count / len(self.prediction_results) * 100

            metrics_text += "预测差异分析:\n"
            metrics_text += f"总样本数: {len(self.prediction_results)}\n"
            metrics_text += f"预测不一致样本数: {disagreement_count}\n"
            metrics_text += f"预测不一致比例: {disagreement_percent:.2f}%\n"

            # 更新指标文本
            self.metrics_text.setText(metrics_text)

            # 绘制对比图表
            self.draw_comparison_chart(has_labels)

            self.statusBar().showMessage("对比分析完成")

        except Exception as e:
            error_msg = f"对比分析失败: {str(e)}\n{traceback.format_exc()}"
            self.metrics_text.setText(error_msg)
            QMessageBox.critical(self, "错误", f"对比分析失败: {str(e)}")

    def draw_comparison_chart(self, has_labels=False):
        """绘制模型对比图表"""
        if self.prediction_results is None:
            return

        # 清除当前图表
        self.chart_canvas.fig.clear()

        # 创建子图
        ax = self.chart_canvas.fig.add_subplot(111)

        # 获取数据
        indices = self.prediction_results['Index']
        basic_probs = self.prediction_results['Basic_Probability']
        enhanced_probs = self.prediction_results['Enhanced_Probability']
        disagreement = self.prediction_results['Disagreement']

        # 绘制概率曲线
        ax.plot(indices, basic_probs, 'b-', alpha=0.7, label='基础模型概率')
        ax.plot(indices, enhanced_probs, 'r-', alpha=0.7, label='增强模型概率')

        # 如果有标签，绘制真实标签
        if has_labels:
            true_labels = self.prediction_results['True_Label']
            ax.plot(indices, true_labels, 'g--', linewidth=2, label='真实标签')

        # 突出显示预测不一致的区域
        disagreement_indices = indices[disagreement]
        if len(disagreement_indices) > 0:
            ax.fill_between(
                disagreement_indices,
                0, 1,
                color='yellow',
                alpha=0.3,
                label='预测不一致区域'
            )

        # 设置图表属性
        ax.set_title('模型预测对比')
        ax.set_xlabel('样本索引')
        ax.set_ylabel('溢流概率/标签')
        ax.set_ylim(-0.05, 1.05)
        ax.legend(loc='upper right')
        ax.grid(True, linestyle='--', alpha=0.7)

        # 添加阈值线
        ax.axhline(y=0.5, color='k', linestyle=':', alpha=0.5)

        # 更新图表
        self.chart_canvas.fig.tight_layout()
        self.chart_canvas.draw()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModelComparisonApp()
    window.show()
    sys.exit(app.exec_())
