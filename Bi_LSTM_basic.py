import os
import re
import sys
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE

# 尝试导入Captum库用于模型可解释性
try:
    import captum
    from captum.attr import IntegratedGradients, LayerIntegratedGradients
    from captum.attr import visualization as viz
    CAPTUM_AVAILABLE = True
except ImportError:
    print("警告: 未找到Captum库，将不能使用特征重要性分析功能")
    CAPTUM_AVAILABLE = False

# 定义基本特征列表
BASIC_FEATURES = ['FlowOutPercent', 'SPP', 'TG', 'Sum']

# 设置随机种子
def set_seed(seed=54):
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
set_seed()

#%% 路径配置
BASE_DIR = r"C:\Users\<USER>\Desktop\毕设\模型"
DATASET_DIR = os.path.join(BASE_DIR, "数据集")  # 数据集文件夹路径

#%% 列名标准化映射（根据图片参数命名更新）
COLUMN_MAP = {
    'SPP': ['SPP', 'SPP立压', '立压', '立管压力'],
    'FlowOut': ['FLOWOUT出口流量', 'FLOWOUT', '出口流量'],
    'Sum': ['Sum', 'PITTOTAL总池体积', 'PITTOTAL', '总池体积', '池体积'],
    'WOB': ['WOB', 'WOB钻压', '钻压'],
    'Torque': ['TORQUE', 'TORQUE扭矩', '扭矩'],
    'FlowOutPercent': ['FLOWOUTPERCENT出口流量百分比', 'FLOWOUTPERCENT', '出口流量百分比'],  # 出口流量百分比
    'TG': ['TG总烃', 'TG', '总烃']  # 总烃
}

# 定义窗口大小常量 (虽然不计算衍生特征，但保留常量名，有助于理解原始权重来源)
# 这些常量直接从原Bi_LSTM.py复制
WINDOW_SIZE = 10
FLOWOUT_WINDOW = 30
SPP_WINDOW = 60
TG_WINDOW = 90
SUM_WINDOW = 120
SHORT_LAG = 30
MEDIUM_LAG = 60
LONG_LAG = 90


#%% 统一列名处理
def unify_columns(df):
    """
    统一数据列名并调整列名格式，增强列名识别能力
    """
    # 保存原始标签（如果存在）
    original_label = None
    label_column = None

    # 检查是否有标签列
    for col in df.columns:
        if 'label' in col.lower() or '标签' in col.lower():
            label_column = col
            original_label = df[col].copy()
            print(f"列名统一前保存标签列: {col}")
            # 打印标签分布
            try:
                label_counts = df[col].value_counts().to_dict()
                print(f"原始标签分布: {label_counts}")
            except:
                print("无法显示原始标签分布")
            break

    original_columns = df.columns.tolist()  # 保存原始列名列表
    # 去除列名中的特殊字符和空格
    df.columns = [re.sub(r'[^\w]', '', col).strip() for col in df.columns]

    # 标准化列名映射 - 只需处理BASIC_FEATURES相关的列
    basic_map_subset = {k: v for k, v in COLUMN_MAP.items() if k in BASIC_FEATURES}
    for std_name, variants in basic_map_subset.items():
        for col in df.columns:
            # 更灵活的列名匹配
            if any(variant.lower() in col.lower() for variant in variants):
                df.rename(columns={col: std_name}, inplace=True)
                break

    # 添加必要的空列，如果不存在 (只针对BASIC_FEATURES)
    for col in BASIC_FEATURES:
        if col not in df.columns:
            df[col] = 0
            print(f"警告：创建默认 {col} 列")

    # 恢复标签列（如果存在）
    if original_label is not None:
        # 如果标签列已经被映射为标准名称，则使用标准名称
        if label_column and label_column.lower() != 'label':
            df['Label'] = original_label
            print(f"将标签列 {label_column} 映射为标准名称 'Label'")
        else:
            # 如果标签列已经是'Label'，直接恢复
            df['Label'] = original_label
            print("恢复标准标签列 'Label'")

        # 打印恢复后的标签分布
        try:
            label_counts = df['Label'].value_counts().to_dict()
            print(f"恢复后标签分布: {label_counts}")
        except:
            print("无法显示恢复后的标签分布")

    # 打印列名映射信息
    print(f"列名映射: 原始 {len(original_columns)} 列 -> 映射后 {len(df.columns)} 列")
    return df

#%% 基本特征预处理
def preprocess_features(df):
    """
    基本特征预处理，处理异常值和零值。
    此版本仅处理 BASIC_FEATURES 中的列。

    参数:
        df: 输入的DataFrame

    返回:
        预处理后的DataFrame
    """
    # 保存原始标签
    original_label = None
    if 'Label' in df.columns:
        print("预处理前保存原始标签")
        original_label = df['Label'].copy()
        # 打印标签分布
        label_counts = df['Label'].value_counts().to_dict()
        print(f"原始标签分布: {label_counts}")

    # 基本特征处理 - 处理异常值和零值
    for col in BASIC_FEATURES:
        if col in df.columns:
            # 检测并替换异常值 (使用3倍标准差作为阈值)
            mean_val = df[col].mean()
            std_val = df[col].std()
            lower_bound = mean_val - 3 * std_val
            upper_bound = mean_val + 3 * std_val

            # 将超出范围的值替换为边界值
            df[col] = df[col].clip(lower_bound, upper_bound)

            # 处理零值和极小值
            if col == 'SPP':
                # 立管压力零值处理 - 小于阈值的视为0
                df[col] = df[col].apply(lambda x: 0 if abs(x) <= 0.05 else x)
            elif col == 'TG':
                # 总烃零值处理 - 小于阈值的视为0
                df[col] = df[col].apply(lambda x: 0 if abs(x) <= 0.01 else x)
            elif col == 'FlowOutPercent':
                 # 出口流量百分比零值处理
                 df[col] = df[col].apply(lambda x: 0 if abs(x) <= 0.01 else x)


    # 处理缺失值 (只对BASIC_FEATURES进行填充，避免填充其他无关列)
    for col in BASIC_FEATURES:
         if col in df.columns:
             if df[col].isna().any():
                 print(f"警告: 发现 {col} 列有缺失值，使用前向填充和后向填充")
                 df[col] = df[col].fillna(method='ffill')
                 df[col] = df[col].fillna(method='bfill')
                 df[col] = df[col].fillna(0) # 最后使用0填充剩余的缺失值


    # 恢复原始标签
    if original_label is not None:
        print("预处理后恢复原始标签")
        df['Label'] = original_label
        # 打印恢复后的标签分布
        label_counts = df['Label'].value_counts().to_dict()
        print(f"恢复后标签分布: {label_counts}")

    return df

# 添加辅助函数处理 .xls 文件 (从原文件复制，以防load_data调用)
def handle_xls_file(filepath):
    """手动处理 .xls 文件"""
    try:
        # 尝试使用 xlrd 1.2.0 版本读取
        import xlrd
        print(f"当前使用的xlrd版本: {xlrd.__version__}")

        # 如果版本太高，需要降级处理
        if xlrd.__version__ >= "2.0.0":
            print("警告: xlrd 2.0及以上版本不支持.xls，尝试替代方案")

            # 替代方案1: 尝试使用第三方模块
            try:
                import pyexcel_xls
                data = pyexcel_xls.get_data(filepath)
                sheet_name = list(data.keys())[0]
                sheet_data = data[sheet_name]

                # 从行数据中提取列名和数据
                headers = sheet_data[0]
                rows = sheet_data[1:]

                return pd.DataFrame(rows, columns=headers)
            except ImportError:
                print("pyexcel_xls 未安装，尝试其他方法...")
            except Exception as e:
                print(f"pyexcel_xls读取失败: {str(e)}")

            # 替代方案2: 尝试将xls文件转换为xlsx (需要win32com)
            try:
                import win32com.client
                import tempfile
                import os

                print("尝试使用Excel应用程序转换文件格式...")
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False

                # 创建临时文件名
                temp_dir = tempfile.gettempdir()
                temp_xlsx = os.path.join(temp_dir, "temp_converted.xlsx")

                try:
                    wb = excel.Workbooks.Open(os.path.abspath(filepath))
                    wb.SaveAs(temp_xlsx, 51)  # 51是xlsx格式的代码
                    wb.Close()
                    excel.Quit()

                    # 读取转换后的文件
                    result_df = pd.read_excel(temp_xlsx, engine='openpyxl')

                    # 删除临时文件
                    try:
                        os.remove(temp_xlsx)
                    except:
                        pass

                    return result_df
                except Exception as conv_err:
                    if 'excel' in locals():
                        excel.Quit()
                    print(f"转换过程中出错: {str(conv_err)}")
                    raise
            except ImportError:
                print("win32com 未安装，尝试其他方法...")
            except Exception as e:
                print(f"使用Excel转换失败: {str(e)}")

            # 如果上述方法都失败，尝试退回到读取CSV (如果存在对应的CSV文件)
            csv_path = filepath.replace('.xls', '.csv')
            if os.path.exists(csv_path):
                print(f"尝试读取同名CSV文件: {csv_path}")
                return pd.read_csv(csv_path)


            raise Exception("所有读取.xls文件的方法均失败")


        else:
            # xlrd 1.x 版本可以正常读取 .xls
            wb = xlrd.open_workbook(filepath)
            sheet = wb.sheet_by_index(0)

            # 从xlrd工作表构建DataFrame
            data = []
            for i in range(sheet.nrows):
                data.append(sheet.row_values(i))

            return pd.DataFrame(data[1:], columns=data[0]) # Use first row as header
    except Exception as e:
        print(f"手动处理.xls文件失败: {str(e)}")
        raise

#%% 数据加载器 (修改版本，仅加载和初步处理，不计算衍生特征)
def load_data():
    """
    从数据集文件夹加载数据，支持从train和test子文件夹分别加载。
    加载后统一列名，进行基础预处理，但不计算衍生特征。

    返回:
        train_df: 训练集DataFrame (仅包含基本特征和Label)
        test_df: 测试集DataFrame (仅包含基本特征和Label)
        如果没有分开的train/test文件夹，则返回(combined_df, None)
    """
    train_dfs = []
    test_dfs = []
    all_dfs = []

    successful_files = []
    skipped_files = []
    error_msgs = []

    # 检查是否有train和test子文件夹
    train_dir = os.path.join(DATASET_DIR, "train")
    test_dir = os.path.join(DATASET_DIR, "test")
    has_separate_folders = os.path.exists(train_dir) and os.path.exists(test_dir)

    if has_separate_folders:
        print("\n检测到train和test子文件夹，将分别加载数据")
        folders_to_process = [
            ("train", train_dir, train_dfs),
            ("test", test_dir, test_dfs)
        ]
    else:
        print("\n未检测到train和test子文件夹，将从主文件夹加载所有数据")
        folders_to_process = [("main", DATASET_DIR, all_dfs)]

    # 处理每个文件夹中的数据
    for folder_name, folder_path, target_dfs in folders_to_process:
        print(f"\n处理{folder_name}文件夹中的数据:")

        for root, _, files in os.walk(folder_path):
            for file in files:
                if not file.lower().endswith(('.xls', '.xlsx', '.csv')): continue # 增加对csv的支持

                print(f"\n尝试处理: {file}")
                try:
                    filepath = os.path.join(root, file)
                    df = None

                    # 多种方法尝试读取文件
                    methods = []
                    if filepath.lower().endswith('.xlsx'):
                         methods.append(lambda: pd.read_excel(filepath, engine='openpyxl'))
                    elif filepath.lower().endswith('.xls'):
                         # 尝试降级xlrd，然后是自定义处理
                         methods.append(lambda: pd.read_excel(filepath, engine='xlrd'))
                         methods.append(lambda: handle_xls_file(filepath)) # 自定义处理函数
                    elif filepath.lower().endswith('.csv'):
                         methods.append(lambda: pd.read_csv(filepath))

                    # Fallback: 尝试不指定引擎读取excel
                    if any(filepath.lower().endswith(ext) for ext in ['.xls', '.xlsx']):
                         methods.append(lambda: pd.read_excel(filepath))

                    last_error = None
                    for method_idx, method in enumerate(methods):
                        try:
                            if method_idx > 0:
                                print(f"尝试方法 {method_idx + 1} 读取文件...")
                            result = method()
                            if result is not None and isinstance(result, pd.DataFrame) and len(result) > 0:
                                df = result
                                break
                        except Exception as e:
                            last_error = str(e)
                            error_msgs.append(f"文件 {file} 方法 {method_idx + 1} 读取失败: {str(e)}")
                            continue

                    if df is None or len(df) == 0:
                        raise Exception(f"所有读取方法均失败, 最后错误: {last_error}")


                    print(f"文件读取成功，原始数据形状: {df.shape}，列名: {df.columns.tolist()[:5]}...")

                    # 统一列名处理
                    df = unify_columns(df)

                    # 基础预处理 (处理异常值和零值)
                    df = preprocess_features(df)

                    # ** 跳过 calculate_constraint_features 和 calculate_DFO **
                    print("跳过衍生特征计算...")

                    # 检查label列
                    # 在 unify_columns 中已经尝试映射和恢复标签列，这里只做最终检查
                    if 'Label' not in df.columns:
                         print(f"跳过文件 {file}，缺少Label列")
                         skipped_files.append(file)
                         continue
                    # 保证标签为整数类型
                    df['Label'] = df['Label'].astype(int)

                    # 添加到对应的数据集列表
                    # ** 只保留 BASIC_FEATURES 和 Label 列 **
                    cols_to_keep = BASIC_FEATURES + ['Label']
                    # 确保要保留的列在df中存在
                    cols_to_keep = [col for col in cols_to_keep if col in df.columns]
                    df = df[cols_to_keep]

                    # 检查是否有 NaN 或 Infinite 值，并填充 (只对BASIC_FEATURES和Label进行填充)
                    if df.isnull().values.any() or np.isinf(df.values).any():
                        print(f"警告: 文件 {file} 数据中包含 NaN 或 Infinite 值，进行填充。")
                        df = df.replace([np.inf, -np.inf], np.nan).fillna(0) # 将Inf替换为NaN再填充0


                    target_dfs.append(df)
                    successful_files.append(filepath)
                    print(f"成功处理: {file}, 形状: {df.shape}")

                except Exception as e:
                    print(f"处理文件 {file} 失败：{str(e)}")
                    #import traceback
                    #traceback.print_exc() # uncomment for detailed error
                    skipped_files.append(file)

    # 处理和合并数据
    print(f"\n成功处理的文件数量: {len(successful_files)}")
    print(f"跳过的文件数量: {len(skipped_files)}")
    if skipped_files:
        print("跳过的文件列表:")
        for file in skipped_files:
            print(f" - {file}")
    if error_msgs:
         print("\n详细错误信息:")
         for msg in error_msgs:
             print(f"- {msg}")


    # 处理列名重复问题的函数 (从原文件复制)
    def handle_duplicate_columns(dfs_list):
        for i, df in enumerate(dfs_list):
            # 检查是否有重复列名
            if len(df.columns) != len(set(df.columns)):
                print(f"警告: 数据框 {i} 有重复列名")
                # 找出重复的列名
                duplicated_cols = [col for col in df.columns if list(df.columns).count(col) > 1]
                print(f"重复列名: {set(duplicated_cols)}")

                # 重命名重复列
                for col in duplicated_cols:
                    # 找出所有重复列的索引
                    indices = [i for i, x in enumerate(df.columns) if x == col]
                    # 保留第一个，重命名其他的
                    for j, idx in enumerate(indices[1:], 1):
                        new_col = f"{col}_{j}"
                        print(f"将重复列 {col} 重命名为 {new_col}")
                        df.columns.values[idx] = new_col

    # 合并数据集的函数 (从原文件复制)
    def merge_dataframes(dfs_list, dataset_name):
        if not dfs_list:
            print(f"\n警告：{dataset_name}数据集为空！")
            return pd.DataFrame()

        try:
            # 处理重复列名
            handle_duplicate_columns(dfs_list)

            # 确保所有数据框都有相同的 BASIC_FEATURES 和 Label 列，并按正确顺序排列
            required_cols = BASIC_FEATURES + ['Label']
            for i, df in enumerate(dfs_list):
                 # 添加缺失的列并填充0
                 for col in required_cols:
                     if col not in df.columns:
                          dfs_list[i][col] = 0
                          print(f"警告: {dataset_name} 数据框 {i} 添加缺失的列 {col} 并用0填充")

                 # 确保列顺序一致
                 dfs_list[i] = dfs_list[i][required_cols]


            # 尝试合并数据集
            merged_df = pd.concat(dfs_list, ignore_index=True)
            print(f"合并后{dataset_name}数据集大小: {merged_df.shape}")


            # 检查是否有Label列
            if 'Label' in merged_df.columns:
                print(f"{dataset_name}标签分布: 0: {(merged_df['Label']==0).sum()}, 1: {(merged_df['Label']==1).sum()}")
            else:
                print(f"警告: 合并后的{dataset_name}数据集中没有'Label'列")
                # 检查是否有类似标签的列 (unify_columns 已处理，这里是双重检查)
                label_cols = [col for col in merged_df.columns if 'label' in col.lower() or '标签' in col.lower()]
                if label_cols:
                    print(f"找到可能的标签列: {label_cols}")
                    # 使用第一个作为标签列
                    merged_df['Label'] = merged_df[label_cols[0]]
                    print(f"将 {label_cols[0]} 设置为标签列")
                    print(f"{dataset_name}标签分布: 0: {(merged_df['Label']==0).sum()}, 1: {(merged_df['Label']==1).sum()}")
                else:
                    print(f"警告: 未能找到或创建'Label'列。")


            return merged_df

        except Exception as e:
            print(f"合并{dataset_name}数据集时出错: {str(e)}")
            #import traceback
            #traceback.print_exc()
            return pd.DataFrame() # Return empty DataFrame on error


    # 根据文件夹结构返回不同的结果
    if has_separate_folders:
        train_df = merge_dataframes(train_dfs, "训练集")
        test_df = merge_dataframes(test_dfs, "测试集")

        # 检查是否有数据
        if len(train_df) == 0 and len(test_df) == 0:
            print("\n警告：训练集和测试集都为空！")
            return pd.DataFrame(), None
        elif len(train_df) == 0:
            print("\n警告：训练集为空，只返回测试集！")
            return test_df, None # This case might need adjustment in UI
        elif len(test_df) == 0:
            print("\n警告：测试集为空，只返回训练集！")
            return train_df, None # This case might need adjustment in UI

        return train_df, test_df
    else:
        combined_df = merge_dataframes(all_dfs, "合并")

        if len(combined_df) == 0:
            print("\n警告：未加载到任何有效数据！")
            return pd.DataFrame(), None

        # 如果是单个合并文件，返回 combined_df 和 None
        return combined_df, None


#%% 模型结构（ BiLSTM with Attention ）
class DrillingModel(nn.Module):
    def __init__(self, input_size=4, hidden_size=64):
        super().__init__()

        # BiLSTM层
        self.lstm = nn.LSTM(input_size, hidden_size,
                          bidirectional=True,
                          num_layers=2,
                          batch_first=True,
                          dropout=0.3)  # 添加dropout以提高泛化能力

        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(hidden_size*2, 32),
            nn.Tanh(),
            nn.Linear(32, 1),
            nn.Softmax(dim=1)
        )

        # 分类器
        self.classifier = nn.Linear(hidden_size*2, 2)

    def forward(self, x):
        # LSTM处理
        lstm_out, _ = self.lstm(x)

        # 注意力机制
        attn_weights = self.attention(lstm_out)
        context = torch.sum(attn_weights * lstm_out, dim=1)

        # 分类
        logits = self.classifier(context)

        return logits

# 将数据转换为时间序列格式
def create_sequences(df_features, df_labels=None, sequence_length=30):
    """
    将特征数据和标签转换为时间序列格式。
    :param df_features: 输入的 DataFrame，只包含特征列。
    :param df_labels: 输入的 Series 或 array，包含标签。如果为 None，则生成全 0 标签。
    :param sequence_length: 序列长度。
    :return: 特征序列 X 和对应的标签 y (NumPy 数组)，以及实际使用的特征列列表。
    """
    # 确保特征列是列表类型
    feature_cols = list(df_features.columns)
    data = df_features.values # 直接使用传入的特征数据

    # 如果标签为 None，生成全 0 标签
    if df_labels is None:
        labels = np.zeros(len(df_features))
    else:
        # 如果是 Series，转换为 NumPy 数组
        if isinstance(df_labels, pd.Series):
            labels = df_labels.values
        else:
            labels = df_labels

    X = []
    sequence_labels = [] # 存储每个序列对应的真实标签 (序列最后一个点)

    # 确保数据点足够生成序列
    if len(data) < sequence_length:
        print(f"警告: 数据点不足 ({len(data)}) 以构建长度为 {sequence_length} 的序列!")
        return np.array([]).astype(np.float32), np.array([]).astype(np.int64), feature_cols

    for i in range(len(data) - sequence_length + 1): # 循环到最后一个完整的序列
        X.append(data[i : i + sequence_length])
        sequence_labels.append(labels[i + sequence_length - 1])  # 标签对应序列的最后一个点

    # 返回 NumPy 数组，并明确返回特征列列表
    return np.array(X).astype(np.float32), np.array(sequence_labels).astype(np.int64), feature_cols

# 训练循环部分
def train_model(model, train_loader, val_loader, test_loader, criterion, optimizer,
                device, epochs=100, patience=10, clip_grad_norm=1.0, callback=None):
    """
    训练模型并实现早停，使用验证集进行早停判断，测试集仅用于最终评估

    参数:
        model: 模型
        train_loader: 训练集数据加载器
        val_loader: 验证集数据加载器，用于早停判断
        test_loader: 测试集数据加载器，仅用于最终评估
        criterion: 损失函数
        optimizer: 优化器
        device: 计算设备
        epochs: 最大训练轮数
        patience: 早停耐心值
        clip_grad_norm: 梯度裁剪阈值
        callback: 回调函数，用于更新UI (此脚本作为独立文件运行时，callback为None)

    返回:
        history: 训练历史记录
        best_val_f1: 最佳验证集F1分数
        best_epoch: 最佳模型的轮数
        test_metrics: 测试集上的评估指标
    """
    best_val_f1 = 0
    best_epoch = 0
    no_improve_epochs = 0
    best_model_state = None # 用于保存最佳模型状态

    print("开始训练...")
    print(f"设备: {device}, 训练样本: {len(train_loader.dataset)}, "
          f"验证样本: {len(val_loader.dataset)}, 测试样本: {len(test_loader.dataset)}")

    history = {'train_loss': [], 'train_acc': [], 'val_acc': [], 'val_f1': []}

    for epoch in range(epochs):
        model.train()
        total_loss = 0
        correct = 0
        total = 0

        for inputs, labels in train_loader:
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()

            optimizer.zero_grad()

            try:
                outputs = model(inputs).float()
                loss = criterion(outputs, labels)

                # 检查损失值是否为NaN
                if torch.isnan(loss).any():
                    print(f"警告: 发现NaN损失! 跳过此批次")
                    continue

                loss.backward()

                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)

                optimizer.step()

                total_loss += loss.item()

                # 计算训练准确度
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

            except RuntimeError as e:
                print(f"训练批次错误: {str(e)}")
                continue


        # 计算平均损失和准确率
        if total > 0:
            avg_loss = total_loss / len(train_loader)
            train_acc = correct / total
            history['train_loss'].append(avg_loss)
            history['train_acc'].append(train_acc)
        else:
            avg_loss = float('nan')
            train_acc = 0

        # 在验证集上评估 - 用于早停判断
        val_acc, val_f1 = evaluate_model(model, val_loader, device)
        history['val_acc'].append(val_acc)
        history['val_f1'].append(val_f1)

        # 打印进度
        print(f"Epoch {epoch+1:03d} | Loss: {avg_loss:.4f} | Train Acc: {train_acc:.4f} | "
              f"Val Acc: {val_acc:.4f} | Val F1: {val_f1:.4f}")

        # 如果提供了回调函数 (UI)，调用它更新UI
        if callback is not None:
            progress = int((epoch + 1) / epochs * 100)
            metrics = {
                 'epoch': epoch + 1,
                 'train_loss': avg_loss,
                 'train_acc': train_acc,
                 'val_acc': val_acc,
                 'val_f1': val_f1 # UI可能需要显示F1
            }
            callback(epoch, avg_loss, train_acc, val_acc, progress)

        # 保存最佳模型 - 基于验证集F1分数
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_epoch = epoch
            best_model_state = model.state_dict().copy()  # 保存模型状态
            torch.save(model.state_dict(), "best_drilling_model_basic.pth") # 使用不同的文件名
            print(f"Epoch {epoch+1:03d} | 保存新的最佳模型 (Val F1: {val_f1:.4f})")
            no_improve_epochs = 0
        else:
            no_improve_epochs += 1

        # 早停 - 基于验证集F1分数
        if no_improve_epochs >= patience:
            print(f"早停! {patience} 轮验证集F1分数没有改善")
            break

    print(f"\n训练完成! 最佳模型在第 {best_epoch+1} 轮, 验证集F1分数: {best_val_f1:.4f}")

    # 加载最佳模型进行测试集评估
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    elif os.path.exists("best_drilling_model_basic.pth"):
         model.load_state_dict(torch.load("best_drilling_model_basic.pth"))
         print("加载保存的最佳模型进行测试集评估")
    else:
         print("警告: 未找到最佳模型文件，使用最后一轮模型进行测试集评估")


    # 在测试集上进行最终评估
    test_acc, test_f1 = evaluate_model(model, test_loader, device)
    print(f"测试集评估结果 - 准确率: {test_acc:.4f}, F1分数: {test_f1:.4f}")

    test_metrics = {'accuracy': test_acc, 'f1': test_f1}

    return history, best_val_f1, best_epoch, test_metrics

def evaluate_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    correct = 0
    total = 0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()

            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)

            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # 计算F1分数
    from sklearn.metrics import f1_score
    # average='binary' 适用于二分类，pos_label=1 指定正类为1
    f1 = f1_score(all_labels, all_preds, average='binary', pos_label=1, zero_division=0)

    return correct / total, f1

def analyze_feature_importance(model, features, device=None):
    """使用Captum分析特征重要性并绘制图表

    参数:
        model: 训练好的模型
        features: 特征名称列表 (此处应为 BASIC_FEATURES)
        device: 计算设备，如果为None则自动检测

    返回:
        importance_dict: 特征重要性字典，键为特征名，值为重要性分数
    """
    if not CAPTUM_AVAILABLE:
        print("警告: Captum库不可用，无法进行特征重要性分析")
        return None

    if model is None:
        print("警告: 模型未加载，无法进行特征重要性分析")
        return None

    try:
        # 确定计算设备
        if device is None:
            device = next(model.parameters()).device

        # 创建一个示例输入数据
        # 确保输入的维度与模型期望的维度匹配 (batch, seq_len, features)
        seq_length = 30  # 使用默认或常用的序列长度
        input_size = len(features)
        # 使用随机数据作为输入示例，形状为 (批量大小, 序列长度, 特征数量)
        # Captum需要 Float Tensor
        inputs = torch.randn(10, seq_length, input_size).to(device).float()

        # 创建集成梯度对象
        # target=1 表示我们关注模型对正类（溢流）的预测
        ig = IntegratedGradients(model)

        # 计算特征重要性
        # 输入是 (批量大小, 序列长度, 特征数量)
        # attributions 的形状将是 (批量大小, 序列长度, 特征数量)
        attributions, delta = ig.attribute(inputs, target=1, return_convergence_delta=True)

        # 验证归因结果
        #print(f"Attributions shape: {attributions.shape}")
        #print(f"Convergence delta: {delta.mean()}") #  delta应该接近0

        # 将归因结果求平均，得到每个特征在序列上的平均重要性
        # attributions.abs() 取绝对值，因为正负归因都表示重要性
        # mean(dim=(0, 1)) 对批量维度 (0) 和序列长度维度 (1) 求平均
        # detach().cpu().numpy() 将结果转移到CPU并转换为NumPy数组
        feature_importance = attributions.abs().mean(dim=(0, 1)).detach().cpu().numpy()

        # 创建特征重要性排序图
        plt.figure(figsize=(8, 5)) # 调整图表大小以适应较少特征

        # 排序特征重要性
        indices = np.argsort(feature_importance)
        sorted_importances = feature_importance[indices]
        sorted_names = [features[i] for i in indices]

        # 绘制水平条形图
        y_pos = np.arange(len(sorted_names))
        plt.barh(y_pos, sorted_importances, align='center')
        plt.yticks(y_pos, sorted_names)
        plt.gca().invert_yaxis()  # 最重要的特征在顶部
        plt.xlabel('特征重要性分数')
        plt.title('特征重要性排序 (基本特征)', fontsize=14)
        plt.tight_layout()

        # 保存图表
        plt.savefig('feature_importance_basic.png', dpi=300, bbox_inches='tight') # 使用不同文件名
        plt.close()  # 关闭图表以释放资源
        print(f"特征重要性图表已保存为 'feature_importance_basic.png'")

        # 返回特征重要性排序结果
        importance_dict = dict(zip(features, feature_importance.tolist()))

        # 打印特征重要性排序
        print("\n最终特征重要性排序:")
        for name, importance in sorted(importance_dict.items(), key=lambda x: x[1], reverse=True):
            print(f"- {name}: {importance:.6f}")

        return importance_dict

    except Exception as e:
        import traceback
        error_msg = f"特征重要性分析失败: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return None


#%% 主程序流程
if __name__ == "__main__":
    # 加载并预处理数据
    print("数据加载中...")
    # 调用修改后的load_data函数
    train_df, test_df = load_data()

    # 检查是否有分开的训练集和测试集
    has_separate_datasets = test_df is not None

    if not has_separate_datasets:
        # 如果没有分开的数据集，使用 combined_df
        df = train_df
        if len(df) == 0:
            print("错误：未加载到有效数据，程序终止")
            sys.exit(1)

        print("处理合并后的数据集...")
        # 确保 BASIC_FEATURES 和 Label 列存在 (load_data已处理，这里是双重检查)
        required_cols = BASIC_FEATURES + ['Label']
        for col in required_cols:
             if col not in df.columns:
                  print(f"错误: 合并后的数据集中缺少必需的列 '{col}'，程序终止。")
                  sys.exit(1)

        # 提取特征和标签 (只使用 BASIC_FEATURES)
        df_features = df[BASIC_FEATURES]
        df_labels = df['Label']

        # 检查是否有 NaN 或 Infinite 值，标准化器对这些值敏感
        if df_features.isnull().values.any() or np.isinf(df_features.values).any():
            print("警告: 最终特征数据中包含 NaN 或 Infinite 值，进行填充。")
            df_features = df_features.replace([np.inf, -np.inf], np.nan).fillna(0) # 将Inf替换为NaN再填充0


        # 数据标准化 (仅在 BASIC_FEATURES 上进行)
        scaler = StandardScaler()
        df_features_scaled = pd.DataFrame(
            scaler.fit_transform(df_features),
            columns=BASIC_FEATURES # 确保列名正确
        )
        print("数据标准化完成 (仅对BASIC_FEATURES)")

        # --- 保存 scaler 的代码 ---
        import joblib
        scaler_filename = "scaler_basic.pkl" # 使用不同的文件名
        joblib.dump(scaler, scaler_filename)
        print(f"标准化器已保存到 {scaler_filename}")
        # ------------------------------

        # --- 添加保存 used_features 的代码 ---
        import json
        features_filename = "used_features_basic.json" # 使用不同的文件名
        with open(features_filename, 'w') as f:
            json.dump(BASIC_FEATURES, f) # 保存明确定义的基本特征列表
        print(f"使用的特征列表已保存到 {features_filename}")
        # ------------------------------------


        # 生成序列数据
        sequence_length = 30 # 定义序列长度
        X, y, used_features = create_sequences(df_features_scaled, df_labels, sequence_length=sequence_length)
        input_size = len(used_features)  # 动态设置输入大小 (应为 len(BASIC_FEATURES))
        print(f"生成的序列特征形状: {X.shape}, 标签形状: {y.shape}, 特征数量: {input_size}")
        print(f"实际使用的特征列: {used_features}")

        # 检查类别分布
        unique_labels, counts = np.unique(y, return_counts=True)
        for label, count in zip(unique_labels, counts):
            print(f"类别 {label} 样本数: {count}，占比: {count/len(y):.2%}")

        # 实现三分法数据划分 (训练集：验证集：测试集 = 6:2:2)
        # 对于时间序列数据，严格按时间顺序划分，确保验证集和测试集的时间点晚于训练集
        test_size = 0.2
        val_size = 0.2

        # 首先划分出测试集
        test_split_idx = int(len(X) * (1 - test_size))
        X_temp, X_test = X[:test_split_idx], X[test_split_idx:]
        y_temp, y_test = y[:test_split_idx], y[test_split_idx:]

        # 然后从剩余数据中划分出验证集
        # 计算验证集在剩余数据中的比例
        val_ratio_in_temp = val_size / (1 - test_size)
        val_split_idx = int(len(X_temp) * (1 - val_ratio_in_temp))

        # 按顺序划分，保持时间连续性
        X_train, X_val = X_temp[:val_split_idx], X_temp[val_split_idx:]
        y_train, y_val = y_temp[:val_split_idx], y_temp[val_split_idx:]

        print("使用三分法时间顺序划分数据集 (训练集：验证集：测试集 = 6:2:2)")

        # 计算最终的三分法比例
        total = len(X)
        train_ratio = len(X_train) / total
        val_ratio = len(X_val) / total
        test_ratio_actual = len(X_test) / total

        print(f"最终三分法比例 - 训练集: {train_ratio:.2f}, 验证集: {val_ratio:.2f}, 测试集: {test_ratio_actual:.2f}")
        print(f"训练集: {len(X_train)}, 验证集: {len(X_val)}, 测试集: {len(X_test)}")

        # 执行数据增强 (仅对训练集)
        print("执行数据增强以扩充训练集...")
        # 找出少数类和多数类样本索引
        minority_indices = np.where(y_train == 1)[0] if np.sum(y_train == 1) < np.sum(y_train == 0) else np.where(y_train == 0)[0]
        majority_indices = np.where(y_train == 0)[0] if np.sum(y_train == 1) < np.sum(y_train == 0) else np.where(y_train == 1)[0]

        # 确定少数类和多数类标签
        minority_label = 1 if np.sum(y_train == 1) < np.sum(y_train == 0) else 0
        majority_label = 0 if minority_label == 1 else 1

        print(f"训练集原始数据形状: {X_train.shape}, 标签分布: {np.bincount(y_train)}")
        print(f"训练集多数类标签为 {majority_label}, 样本数: {len(majority_indices)}")
        print(f"训练集少数类标签为 {minority_label}, 样本数: {len(minority_indices)}")

        # 使用SMOTE进行数据增强
        # 检查少数类样本数量是否足够进行SMOTE
        min_samples_needed = 6  # SMOTE默认k_neighbors=5，需要至少k+1个样本
        if len(minority_indices) >= min_samples_needed:
            try:
                # 将三维序列数据展平为二维进行SMOTE
                n_samples, seq_len, n_features = X_train.shape
                X_reshaped = X_train.reshape(n_samples, seq_len * n_features)

                # 创建SMOTE实例并应用
                smote = SMOTE(random_state=42)
                X_resampled, y_resampled = smote.fit_resample(X_reshaped, y_train)

                # 将结果转回三维形状
                X_train = X_resampled.reshape(-1, seq_len, n_features)
                y_train = y_resampled

                # 打印增强前后的对比
                original_counts = np.bincount(y_train[:n_samples])  # 增强前的部分
                augmented_counts = np.bincount(y_train)
                original_total = n_samples
                augmented_total = len(y_train)

                print(f"SMOTE后训练集形状: {X_train.shape}, 标签分布: {augmented_counts}")
                print(f"增强前训练集标签分布: 0: {original_counts[0] if len(original_counts)>0 else 0} ({original_counts[0]/original_total:.2% if original_total>0 else 0.0:.2%}), 1: {original_counts[1] if len(original_counts)>1 else 0} ({original_counts[1]/original_total:.2% if original_total>0 else 0.0:.2%})")
                print(f"增强后训练集标签分布: 0: {augmented_counts[0] if len(augmented_counts)>0 else 0} ({augmented_counts[0]/augmented_total:.2% if augmented_total>0 else 0.0:.2%}), 1: {augmented_counts[1] if len(augmented_counts)>1 else 0} ({augmented_counts[1]/augmented_total:.2% if augmented_total>0 else 0.0:.2%})")
            except Exception as e:
                print(f"SMOTE增强失败: {str(e)}")
        else:
            print(f"训练集少数类样本数量({len(minority_indices)})不足以进行SMOTE增强(需要至少{min_samples_needed}个)")


    else: # 如果load_data返回了分开的train_df和test_df
        print("处理分开的训练集和测试集...")
        if len(train_df) == 0 or len(test_df) == 0:
            print("错误：训练集或测试集为空，程序终止")
            sys.exit(1)

        # 检查测试集比例是否符合目标比例（20%）
        total_samples = len(train_df) + len(test_df)
        current_test_ratio = len(test_df) / total_samples
        target_test_ratio = 0.2  # 目标测试集比例

        # 如果测试集比例低于目标比例，从训练集补充数据
        if current_test_ratio < target_test_ratio:
            print(f"测试集比例({current_test_ratio:.2f})低于目标比例({target_test_ratio:.2f})，从训练集补充数据...")

            # 计算需要补充的样本数量
            target_test_size = int(total_samples * target_test_ratio)
            samples_to_add = target_test_size - len(test_df)

            # 使用分层抽样从训练集抽取数据补充到测试集
            from sklearn.model_selection import train_test_split

            # 计算抽样比例
            sample_ratio = samples_to_add / len(train_df)

            # 分层抽样
            train_remain, train_to_test = train_test_split(
                train_df,
                test_size=sample_ratio,
                random_state=42,
                stratify=train_df['Label']  # 确保抽样保持类别比例
            )

            print(f"从训练集抽取 {len(train_to_test)} 个样本补充到测试集")
            print(f"抽取前训练集大小: {len(train_df)}, 测试集大小: {len(test_df)}")

            # 更新训练集和测试集
            train_df = train_remain
            test_df = pd.concat([test_df, train_to_test], ignore_index=True)

            print(f"抽取后训练集大小: {len(train_df)}, 测试集大小: {len(test_df)}")
            print(f"新的测试集比例: {len(test_df)/total_samples:.2f}")

        # 提取特征和标签 (只使用 BASIC_FEATURES)
        train_features = train_df[BASIC_FEATURES]
        train_labels = train_df['Label']
        test_features = test_df[BASIC_FEATURES]
        test_labels = test_df['Label']

        # 检查是否有 NaN 或 Infinite 值，标准化器对这些值敏感
        if train_features.isnull().values.any() or np.isinf(train_features.values).any():
            print("警告: 训练集最终特征数据中包含 NaN 或 Infinite 值，进行填充。")
            train_features = train_features.replace([np.inf, -np.inf], np.nan).fillna(0)
        if test_features.isnull().values.any() or np.isinf(test_features.values).any():
            print("警告: 测试集最终特征数据中包含 NaN 或 Infinite 值，进行填充。")
            test_features = test_features.replace([np.inf, -np.inf], np.nan).fillna(0)


        # 数据标准化 - 只在训练集上拟合scaler，然后转换训练集和测试集
        scaler = StandardScaler()
        train_features_scaled = pd.DataFrame(
            scaler.fit_transform(train_features),
            columns=BASIC_FEATURES
        )
        test_features_scaled = pd.DataFrame(
            scaler.transform(test_features),
            columns=BASIC_FEATURES
        )
        print("数据标准化完成 (仅对BASIC_FEATURES)")

        # --- 保存 scaler 的代码 ---
        import joblib
        scaler_filename = "scaler_basic.pkl" # 使用不同的文件名
        joblib.dump(scaler, scaler_filename)
        print(f"标准化器已保存到 {scaler_filename}")
        # ------------------------------

        # --- 添加保存 used_features 的代码 ---
        import json
        features_filename = "used_features_basic.json" # 使用不同的文件名
        with open(features_filename, 'w') as f:
            json.dump(BASIC_FEATURES, f) # 保存明确定义的基本特征列表
        print(f"使用的特征列表已保存到 {features_filename}")
        # ------------------------------------

        # 生成序列数据
        sequence_length = 30
        X_train, y_train, used_features_train = create_sequences(train_features_scaled, train_labels, sequence_length=sequence_length)
        X_test, y_test, used_features_test = create_sequences(test_features_scaled, test_labels, sequence_length=sequence_length)

        # 确保训练和测试序列使用了相同的特征，并且特征数量与 BASIC_FEATURES 一致
        if used_features_train != used_features_test or used_features_train != BASIC_FEATURES:
             print("错误: 训练集和测试集序列特征不匹配或与 BASIC_FEATURES 不一致，程序终止。")
             print(f"训练集特征: {used_features_train}")
             print(f"测试集特征: {used_features_test}")
             print(f"基本特征: {BASIC_FEATURES}")
             sys.exit(1)

        input_size = len(used_features_train)
        print(f"训练序列形状: {X_train.shape}, 标签形状: {y_train.shape}, 特征数量: {input_size}")
        print(f"测试序列形状: {X_test.shape}, 标签形状: {y_test.shape}")
        print(f"实际使用的特征列: {used_features_train}")


        # 检查类别分布 (训练集)
        unique_labels_train, counts_train = np.unique(y_train, return_counts=True)
        for label, count in zip(unique_labels_train, counts_train):
            print(f"训练集类别 {label} 样本数: {count}，占比: {count/len(y_train):.2%}")

        # 从训练集中划分出验证集 (训练集:验证集 = 75%:25%)
        # 注意：先划分验证集，再对剩余的训练集进行SMOTE增强
        val_ratio = 0.25  # 验证集在训练集中的比例
        val_split_idx = int(len(X_train) * (1 - val_ratio))

        # 按时间顺序划分，保持时间连续性
        X_val = X_train[val_split_idx:]
        y_val = y_train[val_split_idx:]
        X_train = X_train[:val_split_idx]
        y_train = y_train[:val_split_idx]

        print(f"从训练集划分出验证集 - 训练集: {len(X_train)}, 验证集: {len(X_val)}, 测试集: {len(X_test)}")

        # 执行数据增强 (仅对训练集)
        print("执行数据增强以扩充训练集...")
        minority_indices_train = np.where(y_train == 1)[0] if np.sum(y_train == 1) < np.sum(y_train == 0) else np.where(y_train == 0)[0]
        majority_indices_train = np.where(y_train == 0)[0] if np.sum(y_train == 1) < np.sum(y_train == 0) else np.where(y_train == 1)[0]
        minority_label_train = 1 if np.sum(y_train == 1) < np.sum(y_train == 0) else 0
        majority_label_train = 0 if minority_label_train == 1 else 1

        print(f"训练集原始数据形状: {X_train.shape}, 标签分布: {np.bincount(y_train)}")
        print(f"训练集多数类标签为 {majority_label_train}, 样本数: {len(majority_indices_train)}")
        print(f"训练集少数类标签为 {minority_label_train}, 样本数: {len(minority_indices_train)}")

        min_samples_needed = 6
        if len(minority_indices_train) >= min_samples_needed:
             try:
                 n_samples, seq_len, n_features = X_train.shape
                 X_reshaped = X_train.reshape(n_samples, seq_len * n_features)
                 smote = SMOTE(random_state=42)
                 X_train_resampled, y_train_resampled = smote.fit_resample(X_reshaped, y_train)
                 X_train = X_train_resampled.reshape(-1, seq_len, n_features)
                 y_train = y_train_resampled

                 original_counts = np.bincount(y_train[:len(y_train_resampled)]) # 检查增强前的部分
                 augmented_counts = np.bincount(y_train)
                 original_total = len(y_train_resampled)
                 augmented_total = len(y_train)


                 print(f"SMOTE后训练集形状: {X_train.shape}, 标签分布: {augmented_counts}")
                 print(f"增强前训练集标签分布: 0: {original_counts[0] if len(original_counts)>0 else 0} ({original_counts[0]/original_total:.2% if original_total>0 else 0.0:.2%}), 1: {original_counts[1] if len(original_counts)>1 else 0} ({original_counts[1]/original_total:.2% if original_total>0 else 0.0:.2%})")
                 print(f"增强后训练集标签分布: 0: {augmented_counts[0] if len(augmented_counts)>0 else 0} ({augmented_counts[0]/augmented_total:.2% if augmented_total>0 else 0.0:.2%}), 1: {augmented_counts[1] if len(augmented_counts)>1 else 0} ({augmented_counts[1]/augmented_total:.2% if augmented_total>0 else 0.0:.2%})")

             except Exception as e:
                 print(f"SMOTE增强失败: {str(e)}")
        else:
             print(f"训练集少数类样本数量({len(minority_indices_train)})不足以进行SMOTE增强(需要至少{min_samples_needed}个)")

        # 计算最终的三分法比例 (基于序列数量)
        total_sequences = len(X_train) + len(X_val) + len(X_test)
        train_ratio = len(X_train) / total_sequences if total_sequences > 0 else 0
        val_ratio = len(X_val) / total_sequences if total_sequences > 0 else 0
        test_ratio_actual = len(X_test) / total_sequences if total_sequences > 0 else 0

        print(f"最终三分法比例 (基于序列数量) - 训练集: {train_ratio:.2f}, 验证集: {val_ratio:.2f}, 测试集: {test_ratio_actual:.2f}")

        # 检查验证集和测试集的正样本比例 (SMOTE仅应用于训练集)
        print(f"验证集正样本比例: {np.sum(y_val == 1)} / {len(y_val)} = {np.sum(y_val == 1)/len(y_val):.2%}")
        print(f"测试集正样本比例: {np.sum(y_test == 1)} / {len(y_test)} = {np.sum(y_test == 1)/len(y_test):.2%}")


    # 数据集类 (从原文件复制)
    class DrillingDataset(Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)  # 确保使用 FloatTensor (32位浮点数)
            self.y = torch.LongTensor(y)

        def __len__(self):
            return len(self.X)

        def __getitem__(self, idx):
            return self.X[idx], self.y[idx]

    # 创建训练、验证和测试数据集
    # 确保数据不为空
    if len(X_train) == 0 or len(X_val) == 0 or len(X_test) == 0:
         print("错误: 数据集划分后存在空集，无法进行训练。请检查数据和划分参数。程序终止。")
         sys.exit(1)

    train_dataset = DrillingDataset(X_train, y_train)
    val_dataset = DrillingDataset(X_val, y_val)
    test_dataset = DrillingDataset(X_test, y_test)

    batch_size = 64
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    # 模型配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = DrillingModel(input_size=input_size).to(device) # input_size 应该为 len(BASIC_FEATURES)

    # 处理类别不平衡 - 确保权重是 float32 (基于训练集标签分布)
    label_counts_train = np.bincount(y_train)
    # 确保至少有两个类别
    if len(label_counts_train) < 2:
        print("错误: 训练集标签少于两个类别，无法计算类别权重。请检查数据。程序终止。")
        sys.exit(1)

    # 反比例加权，少数类权重更大
    class_weight_factor = 1.0  # 降低为1.0，让权重计算更直接反映训练集的不平衡比例 (从原文件复制)
    class_weights = torch.tensor([
        1.0,                                            # 类别 0 (正常) 权重
        label_counts_train[0]/label_counts_train[1] * class_weight_factor # 类别 1 (溢流) 权重
    ], dtype=torch.float32).to(device) # 明确指定 float32 类型

    # 打印权重类型和值以调试
    print(f"Class weights dtype: {class_weights.dtype}")
    print(f"Class weights: {class_weights}")

    criterion = nn.CrossEntropyLoss(weight=class_weights)

    # 确保模型参数为 float32 类型 (尽管模型定义中forward接收float，但参数类型也应一致)
    for param in model.parameters():
        if param.dtype != torch.float32:
             param.data = param.data.float()

    # 优化器配置 (从原文件复制)
    optimizer = optim.AdamW(model.parameters(), lr=5e-4, weight_decay=1e-5)

    # 训练模型 (使用改进的训练函数，基于验证集进行早停)
    # callback=None 因为这个脚本是独立运行的，没有UI回调
    history, best_val_f1, best_epoch, test_metrics = train_model(
        model, train_loader, val_loader, test_loader, criterion, optimizer,
        device, epochs=100, patience=10, clip_grad_norm=1.0, callback=None
    )

    # 可视化训练过程
    try:
        # 使用 matplotlib 直接生成图表，因为没有UI画布
        import matplotlib.pyplot as plt

        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 优先使用微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        # 创建两个子图，分别显示损失和准确率
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 绘制训练损失曲线 - 左侧子图
        epochs_list = list(range(len(history['train_loss']))) # 避免与外部epochs变量冲突
        ax1.plot(epochs_list, history['train_loss'], 'b-', linewidth=2, label='训练集损失')
        ax1.set_title('Training Loss', fontsize=14)
        ax1.set_xlabel('Epoch', fontsize=12)
        ax1.set_ylabel('Loss', fontsize=12)
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.legend(fontsize=10)

        # 设置y轴范围，确保从0开始，便于观察
        y_max_loss = max(history['train_loss']) * 1.1 if history['train_loss'] else 1.0
        ax1.set_ylim(0, y_max_loss)

        # 绘制准确率曲线 - 右侧子图
        ax2.plot(epochs_list, history['train_acc'], 'b-', linewidth=2, label='训练集准确率')
        ax2.plot(epochs_list, history['val_acc'], 'orange', linewidth=2, label='验证集准确率')

        # 设置标题和标签
        ax2.set_title('Accuracy', fontsize=14)
        ax2.set_xlabel('Epoch', fontsize=12)
        ax2.set_ylabel('Accuracy', fontsize=12)
        ax2.grid(True, linestyle='--', alpha=0.7)
        ax2.legend(fontsize=10)

        # 设置y轴范围，通常准确率从0.5开始更有意义
        y_min_acc = min(min(history['train_acc']) if history['train_acc'] else 1.0, min(history['val_acc']) if history['val_acc'] else 1.0)
        y_min_acc = max(0.5, y_min_acc * 0.95)
        y_max_acc = max(max(history['train_acc']) if history['train_acc'] else 0.0, max(history['val_acc']) if history['val_acc'] else 0.0) * 1.05
        y_max_acc = min(1.0, y_max_acc)
        ax2.set_ylim(y_min_acc, y_max_acc)


        # 美化图表
        for ax in [ax1, ax2]:
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.tick_params(axis='both', which='major', labelsize=10)

        # 保存并显式关闭图形
        fig.tight_layout(pad=2.0)
        fig.savefig('training_history_basic.png', dpi=300, bbox_inches='tight') # 使用不同的文件名
        plt.close(fig)  # 明确关闭图形对象
        print(f"训练历史已保存为 'training_history_basic.png'")

    except Exception as e:
        print(f"绘制训练历史图表失败: {e}")

    # 加载最佳模型进行最终评估
    # 使用 basic 版本保存的模型文件
    best_model_path_basic = "best_drilling_model_basic.pth"
    if os.path.exists(best_model_path_basic):
         try:
             model.load_state_dict(torch.load(best_model_path_basic))
             print(f"加载最佳模型: {best_model_path_basic}")
         except Exception as e:
             print(f"加载最佳模型 {best_model_path_basic} 失败: {str(e)}")
             print("使用最后一轮模型进行测试集评估")
    else:
         print("警告: 未找到最佳模型文件 'best_drilling_model_basic.pth'，使用最后一轮模型进行测试集评估")


    # 在测试集上进行最终评估
    model.eval()
    true_labels = []
    pred_labels = []
    pred_probs = []  # 预测概率

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs = inputs.to(device).float()
            outputs = model(inputs)
            probs = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs.data, 1)

            true_labels.extend(labels.cpu().numpy())
            pred_labels.extend(predicted.cpu().numpy())
            pred_probs.extend(probs[:, 1].cpu().numpy())  # 保存正类概率

    # 计算混淆矩阵和分类报告
    from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc

    print("\n混淆矩阵:")
    cm = confusion_matrix(true_labels, pred_labels)
    print(cm)

    print("\n分类报告:")
    # zero_division=0 避免在某些类别中没有样本时的警告/错误
    print(classification_report(true_labels, pred_labels, zero_division=0))


    # 绘制混淆矩阵图 (从原文件复制)
    try:
        import seaborn as sns
        plt.figure(figsize=(6, 5)) # 调整大小
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=['正常', '溢流'],
                    yticklabels=['正常', '溢流'])
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.title('混淆矩阵 (基本特征模型)', fontsize=14)
        plt.tight_layout()
        plt.savefig('confusion_matrix_basic.png', dpi=300) # 使用不同文件名
        plt.close()
        print("混淆矩阵图表已保存为 'confusion_matrix_basic.png'")
    except ImportError:
        print("警告: seaborn 库未安装，跳过混淆矩阵图表绘制")
    except Exception as e:
        print(f"绘制混淆矩阵图表失败: {str(e)}")


    # 绘制ROC曲线
    try:
        fpr, tpr, _ = roc_curve(true_labels, pred_probs)
        roc_auc = auc(fpr, tpr)

        # 创建独立的figure对象
        fig = plt.figure(figsize=(8, 6))
        ax = fig.add_subplot(111)  # 显式创建坐标系

        # 使用axes对象绘图
        ax.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC curve (area = {roc_auc:.4f})') # 调整AUC精度
        ax.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('假阳性率 (FPR)')
        ax.set_ylabel('真阳性率 (TPR)')
        ax.set_title('ROC曲线 (基本特征模型)', fontsize=14)
        ax.legend(loc="lower right")

        # 保存并显式关闭
        fig.savefig('roc_curve_basic.png', dpi=300, bbox_inches='tight') # 使用不同文件名
        plt.close(fig)  # 明确释放资源
        print(f"ROC曲线已保存为 'roc_curve_basic.png', AUC = {roc_auc:.4f}")

    except Exception as e:
        print(f"绘制ROC曲线失败: {str(e)}")

        # 尝试使用替代方法保存ROC数据
        print("尝试使用替代方法绘制ROC数据...")
        try:
            # 如果matplotlib有问题，直接保存ROC数据到CSV
            import pandas as pd
            roc_data = pd.DataFrame({
                'fpr': fpr,
                'tpr': tpr,
                'auc': [roc_auc] * len(fpr)
            })
            roc_data.to_csv('roc_data_basic.csv', index=False) # 使用不同文件名
            print(f"ROC数据已保存为CSV文件，AUC = {roc_auc:.4f}")
        except Exception as e2:
            print(f"保存ROC数据也失败: {str(e2)}")
            print(f"模型AUC值 = {roc_auc:.4f}")


    print(f"\n最佳验证集F1分数: {best_val_f1:.4f} (Epoch {best_epoch+1})")
    print(f"测试集评估结果 - 准确率: {test_metrics['accuracy']:.4f}, F1分数: {test_metrics['f1']:.4f}")
    print("训练完成，最佳模型已保存为 'best_drilling_model_basic.pth'")

    # 分析特征重要性
    if CAPTUM_AVAILABLE:
        print("\n分析特征重要性...")
        # 确保模型加载的是最佳模型
        if os.path.exists(best_model_path_basic):
             try:
                 model.load_state_dict(torch.load(best_model_path_basic))
                 model.eval() # 设置为评估模式
                 print(f"加载最佳模型 {best_model_path_basic} 进行特征重要性分析")
             except Exception as e:
                 print(f"加载最佳模型 {best_model_path_basic} 失败: {str(e)}")
                 print("将使用当前模型进行特征重要性分析")
        else:
             print("警告: 未找到最佳模型文件 'best_drilling_model_basic.pth'，使用当前模型进行特征重要性分析")


        feature_importance = analyze_feature_importance(model, BASIC_FEATURES, device)

        # 将特征重要性保存到文件
        if feature_importance is not None:
            import json

            # 保存特征重要性
            importance_filename = 'feature_importance_basic.json' # 使用不同文件名
            with open(importance_filename, 'w') as f:
                json.dump(feature_importance, f, indent=4) # feature_importance 是 analyze_feature_importance 返回的
            print(f"特征重要性已保存到 '{importance_filename}'")
    else:
        print("\n警告: Captum库不可用，跳过特征重要性分析")

