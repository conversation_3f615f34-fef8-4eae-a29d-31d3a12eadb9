<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>钻井数据分析与溢流预测系统</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="minimumSize">
       <size>
        <width>1000</width>
        <height>700</height>
       </size>
      </property>
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="data_tab">
       <attribute name="title">
        <string>数据准备</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="title">
           <string>数据加载</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <widget class="QLabel" name="label">
               <property name="text">
                <string>数据集路径:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QTextEdit" name="data_path_edit">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>50</height>
                </size>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="browse_btn">
               <property name="text">
                <string>浏览...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QPushButton" name="load_data_btn">
             <property name="text">
              <string>加载数据</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="data_info">
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>数据预处理</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label_2">
               <property name="text">
                <string>样本不平衡处理:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="augment_checkbox">
               <property name="text">
                <string>自动处理样本不平衡</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="label_3">
               <property name="text">
                <string>序列长度:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QSpinBox" name="seq_length">
               <property name="minimum">
                <number>10</number>
               </property>
               <property name="maximum">
                <number>100</number>
               </property>
               <property name="value">
                <number>30</number>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QLabel" name="label_4">
               <property name="text">
                <string>测试集比例:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDoubleSpinBox" name="test_ratio">
               <property name="minimum">
                <double>0.100000000000000</double>
               </property>
               <property name="maximum">
                <double>0.500000000000000</double>
               </property>
               <property name="singleStep">
                <double>0.050000000000000</double>
               </property>
               <property name="value">
                <double>0.200000000000000</double>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QPushButton" name="preprocess_btn">
             <property name="text">
              <string>数据预处理</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="preprocess_info">
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="training_tab">
       <attribute name="title">
        <string>模型训练</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <item>
         <widget class="QGroupBox" name="groupBox_3">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>150</height>
           </size>
          </property>
          <property name="title">
           <string>训练参数</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <item>
            <layout class="QGridLayout" name="gridLayout">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <item>
                <widget class="QLabel" name="label_5">
                 <property name="text">
                  <string>批次大小:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="batch_size">
                 <property name="minimum">
                  <number>8</number>
                 </property>
                 <property name="maximum">
                  <number>256</number>
                 </property>
                 <property name="singleStep">
                  <number>8</number>
                 </property>
                 <property name="value">
                  <number>64</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="0" column="1">
              <layout class="QHBoxLayout" name="horizontalLayout_6">
               <item>
                <widget class="QLabel" name="label_6">
                 <property name="text">
                  <string>学习率:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="learning_rate">
                 <property name="decimals">
                  <number>6</number>
                 </property>
                 <property name="minimum">
                  <double>0.000010000000000</double>
                 </property>
                 <property name="maximum">
                  <double>0.010000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>0.000100000000000</double>
                 </property>
                 <property name="value">
                  <double>0.000500000000000</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="0" column="2">
              <layout class="QHBoxLayout" name="horizontalLayout_7">
               <item>
                <widget class="QLabel" name="label_7">
                 <property name="text">
                  <string>权重衰减:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="weight_decay">
                 <property name="decimals">
                  <number>6</number>
                 </property>
                 <property name="minimum">
                  <double>0.000010000000000</double>
                 </property>
                 <property name="maximum">
                  <double>0.010000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>0.000010000000000</double>
                 </property>
                 <property name="value">
                  <double>0.000010000000000</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <item>
                <widget class="QLabel" name="label_8">
                 <property name="text">
                  <string>类别权重因子:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="class_weight">
                 <property name="minimum">
                  <double>1.000000000000000</double>
                 </property>
                 <property name="maximum">
                  <double>10.000000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>0.100000000000000</double>
                 </property>
                 <property name="value">
                  <double>1.000000000000000</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="1" column="1">
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <item>
                <widget class="QLabel" name="label_9">
                 <property name="text">
                  <string>训练轮次:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="epochs">
                 <property name="minimum">
                  <number>10</number>
                 </property>
                 <property name="maximum">
                  <number>500</number>
                 </property>
                 <property name="singleStep">
                  <number>10</number>
                 </property>
                 <property name="value">
                  <number>100</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item row="1" column="2">
              <layout class="QHBoxLayout" name="horizontalLayout_10">
               <item>
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>早停轮次:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="patience">
                 <property name="minimum">
                  <number>3</number>
                 </property>
                 <property name="maximum">
                  <number>30</number>
                 </property>
                 <property name="value">
                  <number>10</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_4">
          <property name="title">
           <string>训练控制</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <item>
              <widget class="QPushButton" name="train_btn">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="text">
                <string>开始训练</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="stop_btn">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="text">
                <string>停止训练</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QProgressBar" name="progress_bar">
             <property name="value">
              <number>0</number>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="training_info">
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="evaluation_tab">
       <attribute name="title">
        <string>模型评估</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_8">
        <item>
         <widget class="QGroupBox" name="groupBox_5">
          <property name="title">
           <string>评估结果</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_12">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_9">
             <item>
              <widget class="QLabel" name="label_11">
               <property name="text">
                <string>分类评估指标:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QTextEdit" name="metrics_text">
               <property name="readOnly">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_12">
               <property name="text">
                <string>混淆矩阵:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="cm_container" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>200</height>
                </size>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_10">
             <item>
              <widget class="QLabel" name="label_13">
               <property name="text">
                <string>ROC曲线:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="roc_container" native="true"/>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="save_results_btn">
          <property name="text">
           <string>保存评估结果</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="prediction_tab">
       <attribute name="title">
        <string>模型预测</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_11">
        <item>
         <widget class="QGroupBox" name="groupBox_6">
          <property name="title">
           <string>模型加载</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_13">
           <item>
            <widget class="QLabel" name="label_14">
             <property name="text">
              <string>模型路径:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="model_path">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="load_model_btn">
             <property name="text">
              <string>加载模型</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_7">
          <property name="title">
           <string>预测数据</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_12">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_14">
             <item>
              <widget class="QLabel" name="label_15">
               <property name="text">
                <string>预测数据文件:</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QTextEdit" name="pred_data_path">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>30</height>
                </size>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="browse_pred_btn">
               <property name="text">
                <string>浏览...</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QPushButton" name="load_pred_data_btn">
             <property name="text">
              <string>加载预测数据</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTextEdit" name="pred_data_info">
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_8">
          <property name="title">
           <string>预测结果</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_13">
           <item>
            <widget class="QPushButton" name="predict_btn">
             <property name="text">
              <string>执行预测</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QFrame" name="resizable_chart_frame">
             <property name="frameShape">
              <enum>QFrame::StyledPanel</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_14">
              <item>
               <layout class="QHBoxLayout" name="chart_toolbar_layout">
                <item>
                 <widget class="QLabel" name="label_16">
                  <property name="text">
                   <string>图表尺寸:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_17">
                  <property name="text">
                   <string>宽度</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QSpinBox" name="chart_width_spin">
                  <property name="minimum">
                   <number>4</number>
                  </property>
                  <property name="maximum">
                   <number>16</number>
                  </property>
                  <property name="value">
                   <number>8</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_18">
                  <property name="text">
                   <string>高度</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QSpinBox" name="chart_height_spin">
                  <property name="minimum">
                   <number>3</number>
                  </property>
                  <property name="maximum">
                   <number>12</number>
                  </property>
                  <property name="value">
                   <number>6</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="reset_chart_size_btn">
                  <property name="text">
                   <string>重置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QWidget" name="prediction_chart_placeholder" native="true">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>200</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="save_pred_btn">
             <property name="text">
              <string>保存预测结果</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
