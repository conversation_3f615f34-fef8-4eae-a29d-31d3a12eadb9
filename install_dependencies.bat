@echo off
echo 正在安装钻井数据分析与溢流预测系统所需依赖...

REM 检查Python可用性
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b
)

echo 当前使用的Python:
python --version

echo.
echo 安装基础依赖...
python -m pip install --upgrade pip
python -m pip install numpy pandas matplotlib scikit-learn torch

echo.
echo 尝试安装PyQt5...
python -m pip install PyQt5
if %ERRORLEVEL% neq 0 (
    echo PyQt5安装失败，尝试安装PySide2作为替代...
    python -m pip install PySide2
)

echo.
echo 安装已完成，请尝试运行程序
echo 如果仍然遇到问题，请尝试运行simple_gui.py进行环境测试
echo.
pause
