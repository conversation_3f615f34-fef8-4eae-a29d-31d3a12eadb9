import os
import re
import sys  # 添加sys模块用于异常时退出
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler  # 添加MinMaxScaler导入

# 设置随机种子
def set_seed(seed=54):
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
set_seed()

#%% 路径配置
BASE_DIR = r"C:\Users\<USER>\Desktop\毕设\模型"
DATASET_DIR = os.path.join(BASE_DIR, "数据集")  # 数据集文件夹路径

#%% 列名标准化映射（根据图片参数命名更新）
COLUMN_MAP = {
    'HKH': ['HKH大钩高度', 'HKH', '大钩高度'],
    'BITDEPTH': ['BITDEPTH钻头深度', 'BITDEPTH', '钻头深度'],
    'ROP': ['ROP', 'ROP_INST瞬间钻时', '钻速'],
    'SPP': ['SPP', 'SPP立压', '立压', '立管压力'],
    'FlowOut': ['FLOWOUT出口流量', 'FLOWOUT', '出口流量'],
    'Sum': ['Sum', 'PITTOTAL总池体积', 'PITTOTAL', '总池体积', '池体积'],
    'WOB': ['WOB', 'WOB钻压', '钻压'],
    'RPM': ['RPM', 'RPM转盘转速', '转盘转速'],
    'Torque': ['TORQUE', 'TORQUE扭矩', '扭矩'],
    'FlowOutPercent': ['FLOWOUTPERCENT出口流量百分比', 'FLOWOUTPERCENT', '出口流量百分比'],  # 新增出口流量百分比
    'TG': ['TG总烃', 'TG', '总烃']  # 新增总烃
}

#%% 工况判断阈值（严格遵循图片注释）
THRESHOLDS = {
    'rpm_zero': 0.01,       # 转速绝对值≤0.01 视为 0
    'torque_zero': 0.01,    # 扭矩绝对值≤0.01 视为 0 
    'pressure_zero': 0.05,  # 立压绝对值≤0.05 视为 0
    'hook_move': 0.1,       # 大钩移动阈值 (m)
    'window_size': 5        # 趋势分析窗口
}

#%% 统一列名处理（增强兼容性）
def unify_columns(df):
    """
    统一数据列名并调整列名格式，增强列名识别能力
    """
    original_columns = df.columns.tolist()  # 保存原始列名列表
    # 去除列名中的特殊字符和空格
    df.columns = [re.sub(r'[^\w]', '', col).strip() for col in df.columns]
    
    # 标准化列名映射
    for std_name, variants in COLUMN_MAP.items():
        for col in df.columns:
            # 更灵活的列名匹配
            if any(variant.lower() in col.lower() for variant in variants):
                df.rename(columns={col: std_name}, inplace=True)
                break
    
    # 添加必要的空列，如果不存在
    default_cols = ['ROP', 'SPP', 'HKH', 'BITDEPTH', 'Sum', 'RPM', 'Torque']
    for col in default_cols:
        if col not in df.columns:
            df[col] = 0
            print(f"警告：创建默认 {col} 列")
    
    # 打印列名映射信息
    print(f"列名映射: 原始 {len(original_columns)} 列 -> 映射后 {len(df.columns)} 列")
    return df

#%% 动态 DFO 计算
def calculate_DFO(df):
    """
    为了保持代码结构完整，保留此函数，但不使用DFO作为溢流判断特征
    """
    # 创建默认DFO列，但不使用它进行判断
    df['DFO'] = 0
    
    # 如果有FlowOut列，仍然计算DFO，但仅作为记录
    if 'FlowOut' in df.columns and df['FlowOut'].notna().any():
        try:
            df['DFO'] = df['FlowOut'].diff().abs().fillna(0)
            print("基于出口流量计算DFO成功(仅作记录)")
        except Exception as e:
            print(f"警告：计算DFO失败: {str(e)}")
    else:
        print("出口流量数据缺失，DFO 列仅作占位")
    
    return df

#%% 特征预处理（趋势分析）
def preprocess_features(df):
    """生成趋势特征（严格遵循图片↑↓符号要求）"""
    window_size = THRESHOLDS['window_size']
    
    # 大钩高度趋势（↑↓判断）
    df['HKH_trend'] = df['HKH'].rolling(window_size).apply(
        lambda x: 1 if x.mean() > 0 else -1 if x.mean() < 0 else 0, 
        raw=True
    )
    
    # 钻头深度波动（接单根判断）
    df['BITDEPTH_variation'] = df['BITDEPTH'].diff().abs().rolling(window_size).sum()
    
    # 参数零值处理（根据图片注释）
    df['RPM_processed'] = df['RPM'].apply(lambda x: 0 if abs(x) <= THRESHOLDS['rpm_zero'] else x)
    df['Torque_processed'] = df['Torque'].apply(lambda x: 0 if abs(x) <= THRESHOLDS['torque_zero'] else x)
    df['SPP_processed'] = df['SPP'].apply(lambda x: 0 if abs(x) <= THRESHOLDS['pressure_zero'] else x)
    
    return df

#%% 工况判断函数（完全实现图片规则）
def determine_working_condition(row):
    """严格遵循图片中的判断规则表"""
    # 参数获取（使用处理后的参数）
    rpm = row['RPM_processed']
    torque = row['Torque_processed']
    pressure = row['SPP_processed']
    hkh_trend = row['HKH_trend']
    bit_variation = row['BITDEPTH_variation']
    
    # 判断逻辑树
    if rpm == 0 and torque == 0:
        if hkh_trend == -1:    # 大钩高度下降趋势
            return '下钻'
        elif hkh_trend == 1:   # 大钩高度上升趋势
            return '起钻'
        elif bit_variation > 0.5:  # 钻头深度波动明显
            return '接单根'
        else:
            return '停钻'
        
    elif pressure > 0:
        if hkh_trend == 1 and torque > 0:
            return '划眼'
        elif hkh_trend == -1 and torque > 0:
            return '倒划眼'
        else:
            return '循环'
        
    elif rpm > 0 and torque > 0:
        return '钻进'
    
    return '未知'

#%% 动态标签生成（按新的溢流特征检测规则）
def generate_labels(df):
    """
    根据溢流特征检测规则生成标签：
    1. FLOWOUTPERCENT出口流量百分比增大
    2. SPP立压下降
    3. ROP钻速加快
    4. Sum总池体积持续增大
    5. TG总烃持续增大（气侵特征）
    
    暂不使用出口流量(FlowOut)和出口流量差(DFO)
    """
    df['Label'] = 0
    
    # 计算滑动窗口内的趋势变化
    window = 10
    
    # 基本特征趋势计算
    df['ROP_trend'] = df['ROP'].rolling(window).mean().diff()  # 钻速趋势
    df['SPP_trend'] = df['SPP'].rolling(window).mean().diff()  # 立压趋势
    
    # 有条件计算其他趋势
    # 总池体积趋势
    if 'Sum' in df.columns and df['Sum'].notna().any() and df['Sum'].abs().sum() > 0:
        df['Sum_trend'] = df['Sum'].rolling(window).mean().diff()
        print("检测到有效的总池体积数据")
    else:
        df['Sum_trend'] = 0
        print("缺少有效的总池体积数据，趋势填充为0")
    
    # 出口流量百分比趋势
    if 'FlowOutPercent' in df.columns and df['FlowOutPercent'].notna().any() and df['FlowOutPercent'].abs().sum() > 0:
        df['FlowOutPercent_trend'] = df['FlowOutPercent'].rolling(window).mean().diff()
        print("检测到有效的出口流量百分比数据")
    else:
        df['FlowOutPercent_trend'] = 0
        print("缺少有效的出口流量百分比数据，趋势填充为0")
    
    # 总烃趋势
    if 'TG' in df.columns and df['TG'].notna().any() and df['TG'].abs().sum() > 0:
        df['TG_trend'] = df['TG'].rolling(window).mean().diff()
        print("检测到有效的总烃数据")
    else:
        df['TG_trend'] = 0
        print("缺少有效的总烃数据，趋势填充为0")

    # 填充缺失值
    trend_cols = ['ROP_trend', 'SPP_trend', 'Sum_trend', 'FlowOutPercent_trend', 'TG_trend']
    df[trend_cols] = df[trend_cols].fillna(0)
    
    # 溢流特征检测条件定义
    rop_cond = df['ROP_trend'] > 0.2  # 钻速加快
    spp_cond = df['SPP_trend'] < -0.1  # 立压下降
    sum_cond = df['Sum_trend'] > 0.1 if df['Sum'].abs().sum() > 0 else pd.Series(False, index=df.index)  # 总池体积增大
    flow_percent_cond = df['FlowOutPercent_trend'] > 0.1 if ('FlowOutPercent' in df.columns and df['FlowOutPercent'].abs().sum() > 0) else pd.Series(False, index=df.index)  # 出口流量百分比上升
    tg_cond = df['TG_trend'] > 0.1 if ('TG' in df.columns and df['TG'].abs().sum() > 0) else pd.Series(False, index=df.index)  # 总烃增大(气侵)
    
    # 溢流判断特征:
    # 1. 出口流量百分比增大（首要特征）
    # 2. 立压下降
    # 3. 钻速加快
    # 4. 总池体积持续增大
    # 以上4点至少满足3点
    # 5. 总烃持续增大（气侵特征，现将其也视为溢流）
    
    # 计算满足特征的数量 (不包括总烃)
    feature_conditions = [flow_percent_cond, spp_cond, rop_cond, sum_cond]
    condition_count = sum(cond.astype(int) for cond in feature_conditions)
    
    # 溢流条件1: 至少满足3个条件且必须包含出口流量百分比增大(首要特征)
    overflow_condition = (condition_count >= 3) & flow_percent_cond
    
    # 溢流条件2(气侵): 总烃增大且至少满足2个其他条件
    # 气侵现在也算作溢流的一种类型
    gas_invasion_condition = tg_cond & (condition_count >= 2)
    
    # 直接应用溢流标记 - 满足溢流条件或气侵条件
    df.loc[(overflow_condition | gas_invasion_condition), 'Label'] = 1
    
    # 输出标签统计信息
    label_stats = df['Label'].value_counts()
    print(f"标签统计: 正常样本={label_stats.get(0, 0)}，溢流样本={label_stats.get(1, 0)}")
    
    return df

#%% 增强型数据加载器
def load_data():
    all_dfs = []
    successful_files = []
    skipped_files = []
    error_msgs = []
    
    print("\n处理数据集文件夹中的数据:")
    for root, _, files in os.walk(DATASET_DIR):
        for file in files:
            if not file.endswith(('.xls', '.xlsx')): continue
            
            print(f"\n尝试处理: {file}")
            try:
                # 从文件名提取工况（如果文件名包含工况）
                match = re.search(r'-([\u4e00-\u9fa5]+)\.', file)
                working_cond = match.group(1) if match else None
                
                # 读取文件，处理可能的编码问题
                filepath = os.path.join(root, file)
                df = None  # 初始化DataFrame
                
                # 多种方法尝试读取文件
                methods = [
                    # 方法1: 使用openpyxl读取 .xlsx 文件
                    lambda: pd.read_excel(filepath, engine='openpyxl') if filepath.endswith('.xlsx') else None,
                    
                    # 方法2: 尝试降级xlrd来读取 .xls 文件
                    lambda: pd.read_excel(filepath, engine='xlrd') if filepath.endswith('.xls') else None,
                    
                    # 方法3: 不指定引擎，让pandas自动选择
                    lambda: pd.read_excel(filepath),
                    
                    # 方法4: 使用xlrd自行处理 .xls 文件
                    lambda: handle_xls_file(filepath) if filepath.endswith('.xls') else None,
                ]
                
                # 依次尝试各种读取方法
                last_error = None
                for method_idx, method in enumerate(methods):
                    try:
                        if method_idx > 0:
                            print(f"尝试方法 {method_idx + 1} 读取文件...")
                        result = method()
                        if result is not None and isinstance(result, pd.DataFrame) and len(result) > 0:
                            df = result
                            break
                    except Exception as e:
                        last_error = str(e)
                        error_msgs.append(f"方法 {method_idx + 1} 读取失败: {str(e)}")
                        continue
                
                if df is None or len(df) == 0:
                    raise Exception(f"所有读取方法均失败, 最后错误: {last_error}")
                
                print(f"文件读取成功，原始数据形状: {df.shape}，列名: {df.columns.tolist()[:5]}...")
                
                # 统一列名处理(需要先运行)
                df = unify_columns(df)
                
                # 生成必要的特征
                df = calculate_DFO(df)
                df = preprocess_features(df)
                
                # 设置工况: 使用文件名中的工况或通过函数动态判断
                if working_cond:
                    df['WorkingCondition'] = working_cond
                    print(f"从文件名提取工况: {working_cond}")
                else:
                    df['WorkingCondition'] = df.apply(lambda x: determine_working_condition(x), axis=1)
                    conditions = df['WorkingCondition'].value_counts().to_dict()
                    print(f"动态判断工况分布: {conditions}")
                
                # 检查必需列 - 降低要求，只需要ROP和SPP
                required_cols = ['ROP', 'SPP','FlowOutPercent']
                if not all(col in df.columns for col in required_cols):
                    print(f"跳过文件 {file}，缺少最低必需列: {required_cols}")
                    skipped_files.append(file)
                    continue
                
                # 生成标签
                df = generate_labels(df)
                
                all_dfs.append(df)
                successful_files.append(filepath)
                print(f"成功处理: {file}, 形状: {df.shape}")
                
            except Exception as e:
                print(f"处理文件 {file} 失败：{str(e)}")
                import traceback
                traceback.print_exc()
                skipped_files.append(file)
    
    # 如果没有有效数据，返回空 DataFrame
    if not all_dfs:
        print("\n警告：未加载到任何有效数据！")
        return pd.DataFrame()
    
    print(f"\n成功处理的文件数量: {len(successful_files)}")
    print(f"跳过的文件数量: {len(skipped_files)}")
    if skipped_files:
        print("跳过的文件列表:")
        for file in skipped_files:
            print(f" - {file}")
    
    # 合并数据集
    combined_df = pd.concat(all_dfs, ignore_index=True)
    print(f"合并后数据集大小: {combined_df.shape}")
    print(f"标签分布: 0: {(combined_df['Label']==0).sum()}, 1: {(combined_df['Label']==1).sum()}")
    
    return combined_df

# 添加辅助函数处理 .xls 文件
def handle_xls_file(filepath):
    """手动处理 .xls 文件"""
    try:
        # 尝试使用 xlrd 1.2.0 版本读取
        import xlrd
        print(f"当前使用的xlrd版本: {xlrd.__version__}")
        
        # 如果版本太高，需要降级处理
        if xlrd.__version__ >= "2.0.0":
            print("警告: xlrd 2.0及以上版本不支持.xls，尝试替代方案")
            
            # 替代方案1: 尝试使用第三方模块
            try:
                import pyexcel_xls
                data = pyexcel_xls.get_data(filepath)
                sheet_name = list(data.keys())[0]
                sheet_data = data[sheet_name]
                
                # 从行数据中提取列名和数据
                headers = sheet_data[0]
                rows = sheet_data[1:]
                
                return pd.DataFrame(rows, columns=headers)
            except ImportError:
                print("pyexcel_xls 未安装，尝试其他方法...")
            except Exception as e:
                print(f"pyexcel_xls读取失败: {str(e)}")
            
            # 替代方案2: 尝试将xls文件转换为xlsx (需要win32com)
            try:
                import win32com.client
                import tempfile
                import os
                
                print("尝试使用Excel应用程序转换文件格式...")
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False
                
                # 创建临时文件名
                temp_dir = tempfile.gettempdir()
                temp_xlsx = os.path.join(temp_dir, "temp_converted.xlsx")
                
                try:
                    wb = excel.Workbooks.Open(os.path.abspath(filepath))
                    wb.SaveAs(temp_xlsx, 51)  # 51是xlsx格式的代码
                    wb.Close()
                    excel.Quit()
                    
                    # 读取转换后的文件
                    result_df = pd.read_excel(temp_xlsx, engine='openpyxl')
                    
                    # 删除临时文件
                    try:
                        os.remove(temp_xlsx)
                    except:
                        pass
                        
                    return result_df
                except Exception as conv_err:
                    if 'excel' in locals():
                        excel.Quit()
                    print(f"转换过程中出错: {str(conv_err)}")
                    raise
            except ImportError:
                print("win32com 未安装，尝试其他方法...")
            except Exception as e:
                print(f"使用Excel转换失败: {str(e)}")
                
            # 如果上述方法都失败，尝试退回到读取CSV (如果存在对应的CSV文件)
            csv_path = filepath.replace('.xls', '.csv')
            if os.path.exists(csv_path):
                print(f"尝试读取同名CSV文件: {csv_path}")
                return pd.read_csv(csv_path)
                
            raise Exception("所有读取.xls文件的方法均失败")
                
        else:
            # xlrd 1.x 版本可以正常读取 .xls
            wb = xlrd.open_workbook(filepath)
            sheet = wb.sheet_by_index(0)
            
            # 从xlrd工作表构建DataFrame
            data = []
            for i in range(sheet.nrows):
                data.append(sheet.row_values(i))
                
            # 提取列名和数据行
            headers = data[0]
            rows = data[1:]
            
            return pd.DataFrame(rows, columns=headers)
    except Exception as e:
        print(f"手动处理.xls文件失败: {str(e)}")
        raise

#%% 模型结构（BiLSTM with Attention）
class DrillingModel(nn.Module):
    def __init__(self, input_size=4, hidden_size=64):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, 
                          bidirectional=True,
                          num_layers=2,
                          batch_first=True)
        self.attention = nn.Sequential(
            nn.Linear(hidden_size*2, 32),
            nn.Tanh(),
            nn.Linear(32, 1),
            nn.Softmax(dim=1)
        )
        self.classifier = nn.Linear(hidden_size*2, 2)
        
    def forward(self, x):
        out, _ = self.lstm(x)
        attn_weights = self.attention(out)
        context = torch.sum(attn_weights * out, dim=1)
        return self.classifier(context)

# 将数据转换为时间序列格式
def create_sequences(df, sequence_length=30):
    """
    将数据转换为时间序列格式，动态选择特征列。
    :param df: 输入的 DataFrame，包含特征和标签。
    :param sequence_length: 序列长度。
    :return: 特征序列 X 和对应的标签 y。
    """
    # 动态选择有效的特征列（有非零数据的列）
    # 增加FlowOutPercent和TG到可用特征中，移除DFO
    available_features = ['ROP', 'SPP', 'RPM', 'Sum', 'FlowOutPercent', 'TG']
    feature_cols = []
    
    for col in available_features:
        if col in df.columns and df[col].notna().any() and df[col].abs().sum() > 0:
            feature_cols.append(col)
    
    if len(feature_cols) < 2:  # 至少需要两个特征
        print(f"警告：有效特征列不足，当前特征: {feature_cols}")
        # 确保至少有FlowOutPercent和Sum特征
        if 'FlowOutPercent' not in feature_cols:
            feature_cols.append('FlowOutPercent')
        if 'Sum' not in feature_cols:
            feature_cols.append('Sum')
    
    print(f"用于序列生成的特征列: {feature_cols}")
    label_col = 'Label'  # 标签列
    
    X, y = [], []
    data = df[feature_cols].values
    labels = df[label_col].values
    
    for i in range(len(data) - sequence_length):
        X.append(data[i:i + sequence_length])
        y.append(labels[i + sequence_length - 1])  # 标签对应序列的最后一个点
    
    return np.array(X), np.array(y), feature_cols  # 返回使用的特征列

# 训练循环部分
def train_model(model, train_loader, test_loader, criterion, optimizer, 
                device, epochs=100, patience=10, clip_grad_norm=1.0):
    """
    训练模型并实现早停
    """
    best_test_acc = 0
    best_epoch = 0
    no_improve_epochs = 0
    
    print("开始训练...")
    print(f"设备: {device}, 训练样本: {len(train_loader.dataset)}, 测试样本: {len(test_loader.dataset)}")
    
    history = {'train_loss': [], 'train_acc': [], 'test_acc': []}
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for inputs, labels in train_loader:
            # 确保数据类型正确
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()
            
            optimizer.zero_grad()
            
            try:
                # 打印用于调试
                if epoch == 0 and total == 0:
                    print(f"输入形状: {inputs.shape}, 类型: {inputs.dtype}")
                    print(f"标签分布: {torch.bincount(labels)}")
                
                outputs = model(inputs).float()
                loss = criterion(outputs, labels)
                
                # 检查损失值是否为NaN
                if torch.isnan(loss).any():
                    print(f"警告: 发现NaN损失! 跳过此批次")
                    continue
                    
                loss.backward()
                
                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)
                
                optimizer.step()
                
                total_loss += loss.item()
                
                # 计算训练准确度
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
                
            except RuntimeError as e:
                print(f"训练批次错误: {str(e)}")
                continue
        
        # 计算平均损失和准确率
        if total > 0:  
            avg_loss = total_loss / len(train_loader)
            train_acc = correct / total
            history['train_loss'].append(avg_loss)
            history['train_acc'].append(train_acc)
        else:
            avg_loss = float('nan')
            train_acc = 0
            
        # 在测试集上评估
        test_acc, test_f1 = evaluate_model(model, test_loader, device)
        history['test_acc'].append(test_acc)
        
        # 打印进度
        print(f"Epoch {epoch+1:03d} | Loss: {avg_loss:.4f} | Train Acc: {train_acc:.4f} | "
              f"Test Acc: {test_acc:.4f} | F1: {test_f1:.4f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            torch.save(model.state_dict(), "best_drilling_model.pth")
            print(f"Epoch {epoch+1:03d} | 保存新的最佳模型")
            no_improve_epochs = 0
        else:
            no_improve_epochs += 1
            
        # 早停
        if no_improve_epochs >= patience:
            print(f"早停! {patience} 轮没有改善")
            break
    
    print(f"\n训练完成! 最佳模型在第 {best_epoch+1} 轮, 测试准确率: {best_test_acc:.4f}")
    return history, best_test_acc, best_epoch

def evaluate_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()
            
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)
            
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # 计算F1分数
    from sklearn.metrics import f1_score
    f1 = f1_score(all_labels, all_preds, average='binary')
    
    return correct / total, f1

# Focal Loss实现
class FocalLoss(nn.Module):
    def __init__(self, alpha=1, gamma=2, weight=None):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.weight = weight

    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, weight=self.weight, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()

# 特征增强：增加一阶/二阶差分和窗口统计量
def enhance_features(df):
    window = 10
    for col in ['ROP', 'SPP', 'RPM', 'Sum', 'FlowOutPercent', 'TG']:
        if col in df.columns:
            df[f'{col}_diff1'] = df[col].diff().fillna(0)
            df[f'{col}_diff2'] = df[col].diff().diff().fillna(0)
            df[f'{col}_max'] = df[col].rolling(window).max().fillna(method='bfill')
            df[f'{col}_min'] = df[col].rolling(window).min().fillna(method='bfill')
            df[f'{col}_std'] = df[col].rolling(window).std().fillna(0)
    return df

#%% 主程序流程
if __name__ == "__main__":
    # 加载并预处理数据
    print("数据加载中...")
    df = load_data()
    
    if len(df) == 0:
        print("错误：未加载到有效数据，程序终止")
        sys.exit(1)
    
    # 特征增强
    df = enhance_features(df)

    # 数据标准化 - 动态确定要使用的特征列
    available_features = [col for col in df.columns if any(
        key in col for key in ['ROP', 'SPP', 'RPM', 'Sum', 'FlowOutPercent', 'TG']
    )]
    feature_cols = [col for col in available_features if df[col].abs().sum() > 0]
    print(f"标准化使用的特征列: {feature_cols}")
    
    # 移除异常值
    for col in feature_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower = Q1 - 1.5 * IQR
        upper = Q3 + 1.5 * IQR
        df[col] = np.clip(df[col], lower, upper)
    
    # Min-Max标准化处理
    scaler = MinMaxScaler(feature_range=(0, 1))
    df[feature_cols] = scaler.fit_transform(df[feature_cols])
    
    # 检查是否有NaN值，并填充
    if df.isna().any().any():
        print("警告: 发现NaN值，使用0填充")
        df = df.fillna(0)
    
    # 生成序列数据
    X, y, used_features = create_sequences(df)
    input_size = len(used_features)  # 动态设置输入大小
    print(f"生成的序列特征形状: {X.shape}, 标签形状: {y.shape}, 特征数量: {input_size}")
    
    # 检查类别分布
    unique_labels, counts = np.unique(y, return_counts=True)
    for label, count in zip(unique_labels, counts):
        print(f"类别 {label} 样本数: {count}，占比: {count/len(y):.2%}")
    
    # 处理数据的极度不平衡 (使用过采样方法)
    if np.sum(y == 1) / len(y) < 0.1:  # 如果正样本比例小于10%
        from sklearn.utils import resample
        # 找出少数类和多数类样本索引
        minority_indices = np.where(y == 1)[0]
        majority_indices = np.where(y == 0)[0]
        
        # 获取少数类和多数类样本
        X_minority = X[minority_indices]
        y_minority = y[minority_indices]
        X_majority = X[majority_indices]
        y_majority = y[majority_indices]
        
        # 过采样少数类，让它达到多数类的一定比例 
        target_samples = int(len(majority_indices) * 0.2)
        if len(minority_indices) < target_samples:
            # 只有在少数类样本确实少于目标数量时才过采样
            X_minority_resampled, y_minority_resampled = resample(
                X_minority, 
                y_minority, 
                replace=True, 
                n_samples=target_samples,
                random_state=42
            )
            
            # 合并回数据集
            X = np.vstack((X_majority, X_minority_resampled))
            y = np.hstack((y_majority, y_minority_resampled))
            
            print(f"过采样后数据集形状: {X.shape}, 标签分布: {np.bincount(y)}")
        
    # 划分训练集和测试集 (80% 训练, 20% 测试)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"数据集总大小: {len(X)} 序列")
    print(f"训练集大小: {len(X_train)} 序列, 正样本: {np.sum(y_train == 1)}, 比例: {np.sum(y_train == 1)/len(y_train):.2%}")
    print(f"测试集大小: {len(X_test)} 序列, 正样本: {np.sum(y_test == 1)}, 比例: {np.sum(y_test == 1)/len(y_test):.2%}")
    
    # 数据集类
    class DrillingDataset(Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)  # 确保使用 FloatTensor (32位浮点数)
            self.y = torch.LongTensor(y)
            
        def __len__(self):
            return len(self.X)
        
        def __getitem__(self, idx):
            return self.X[idx], self.y[idx]
    
    # 创建训练和测试数据集
    train_dataset = DrillingDataset(X_train, y_train)
    test_dataset = DrillingDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=64, shuffle=False)
    
    # 模型配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = DrillingModel(input_size=input_size).to(device)  # 使用动态特征数量
    
    # 处理类别不平衡 - 确保权重是 float32
    label_counts = np.bincount(y_train)
    # 反比例加权，少数类权重更大
    class_weights = torch.tensor([
        1.0,                                  # 类别 0 权重
        label_counts[0]/label_counts[1] * 2.0 # 类别 1 权重 (少数类)
    ], dtype=torch.float32).to(device)  # 明确指定 float32 类型
    
    # 打印权重类型以调试
    print(f"Class weights dtype: {class_weights.dtype}")
    print(f"Class weights: {class_weights}")
    
    # 损失函数选择（可切换FocalLoss）
    # criterion = nn.CrossEntropyLoss(weight=class_weights)
    criterion = FocalLoss(alpha=1, gamma=2, weight=class_weights)
    
    # 确保模型参数为 float32 类型
    for param in model.parameters():
        param.data = param.data.float()
    
    # 优化器配置 (使用更小的学习率)
    optimizer = optim.AdamW(model.parameters(), lr=5e-4, weight_decay=1e-5)
    
    # 训练模型 (使用改进的训练函数)
    history, best_test_acc, best_epoch = train_model(
        model, train_loader, test_loader, criterion, optimizer, 
        device, epochs=100, patience=10, clip_grad_norm=1.0
    )
    
    # 可视化训练过程
    try:
        import matplotlib.pyplot as plt
    
        # 使用面向对象API创建figure和axes
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
        # 绘制损失曲线
        ax1.plot(history['train_loss'], label='Train Loss')
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
    
        # 绘制准确率曲线
        ax2.plot(history['train_acc'], label='Train Accuracy')
        ax2.plot(history['test_acc'], label='Test Accuracy')
        ax2.set_title('Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
    
        # 保存并显式关闭图形
        fig.tight_layout()
        fig.savefig('training_history.png', dpi=300)
        plt.close(fig)  # 明确关闭图形对象
        print(f"训练历史已保存为 'training_history.png'")
    
    except Exception as e:
        print(f"绘制图表失败: {e}")
    
    # 加载最佳模型进行最终评估
    model.load_state_dict(torch.load("best_drilling_model.pth"))
    
    # 在测试集上进行最终评估
    model.eval()
    true_labels = []
    pred_labels = []
    pred_probs = []  # 预测概率
    
    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs = inputs.to(device).float()
            outputs = model(inputs)
            probs = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs.data, 1)
            
            true_labels.extend(labels.cpu().numpy())
            pred_labels.extend(predicted.cpu().numpy())
            pred_probs.extend(probs[:, 1].cpu().numpy())  # 保存正类概率
    
    # 计算混淆矩阵和分类报告
    from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
    
    print("\n混淆矩阵:")
    cm = confusion_matrix(true_labels, pred_labels)
    print(cm)
    
    print("\n分类报告:")
    print(classification_report(true_labels, pred_labels))
    
    # 绘制ROC曲线
    try:
        fpr, tpr, _ = roc_curve(true_labels, pred_probs)
        roc_auc = auc(fpr, tpr)
    
        # 创建独立的figure对象
        fig = plt.figure(figsize=(8, 6))
        ax = fig.add_subplot(111)  # 显式创建坐标系
    
        # 使用axes对象绘图
        ax.plot(fpr, tpr, color='darkorange', lw=2, 
                label=f'ROC curve (area = {roc_auc:.2f})')
        ax.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title('Receiver Operating Characteristic')
        ax.legend(loc="lower right")
    
        # 保存并显式关闭
        fig.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
        plt.close(fig)  # 明确释放资源
        print(f"ROC曲线已保存为 'roc_curve.png', AUC = {roc_auc:.4f}")

    except Exception as e:
        print(f"绘制ROC曲线失败: {str(e)}")
        
        # 尝试使用替代方法保存ROC数据
        print("尝试使用替代方法绘制ROC数据...")
        try:
            # 如果matplotlib有问题，直接保存ROC数据到CSV
            import pandas as pd
            from sklearn.preprocessing import MinMaxScaler
            roc_data = pd.DataFrame({
                'fpr': fpr, 
                'tpr': tpr,
                'auc': [roc_auc] * len(fpr)
            })
            roc_data.to_csv('roc_data.csv', index=False)
            print(f"ROC数据已保存为CSV文件，AUC = {roc_auc:.4f}")
        except Exception as e2:
            print(f"保存ROC数据也失败: {str(e2)}")
            print(f"模型AUC值 = {roc_auc:.4f}")
    
    print(f"\n最佳测试准确率: {best_test_acc:.4f} (Epoch {best_epoch+1})")
    print("训练完成，最佳模型已保存为 'best_drilling_model.pth'")


