import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 加载数据
def load_data(file_path):
    try:
        # 尝试使用不同的引擎读取Excel文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, engine='openpyxl')
        elif file_path.endswith('.xls'):
            df = pd.read_excel(file_path, engine='xlrd')
        else:
            df = pd.read_excel(file_path)

        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return None

# 统一列名
def unify_columns(df):
    # 创建列名映射字典
    column_mapping = {
        'spp': 'SPP', 'SPP': 'SPP', 'standpipe pressure': 'SPP', '立管压力': 'SPP',
        'tg': 'TG', 'TG': 'TG', 'total gas': 'TG', '总烃': 'TG',
        'sum': 'Sum', 'Sum': 'Sum', 'pit volume total': 'Sum', '总池体积': 'Sum',
        'flow_out_percent': 'FlowOutPercent', 'FlowOutPercent': 'FlowOutPercent',
        'flow out percent': 'FlowOutPercent', '出口流量百分比': 'FlowOutPercent',
        'label': 'Label', 'Label': 'Label', '标签': 'Label'
    }

    # 统一列名
    renamed_columns = {}
    for col in df.columns:
        col_lower = col.lower().strip()
        for key in column_mapping:
            if key.lower() in col_lower:
                renamed_columns[col] = column_mapping[key]
                break

    # 应用重命名
    df = df.rename(columns=renamed_columns)

    return df

# 查找溢流事件
def find_overflow_event(df):
    if 'Label' in df.columns:
        # 如果有标签列，查找标记为溢流的区域
        overflow_indices = np.where(df['Label'] == 1)[0]
        if len(overflow_indices) > 0:
            # 找到最长的连续溢流区域
            from itertools import groupby
            from operator import itemgetter

            # 找到所有连续的溢流区域
            consecutive_regions = []
            for k, g in groupby(enumerate(overflow_indices), lambda ix: ix[0] - ix[1]):
                consecutive_indices = list(map(itemgetter(1), g))
                consecutive_regions.append(consecutive_indices)

            # 选择最长的连续区域
            longest_region = max(consecutive_regions, key=len)

            # 找到这个区域的前后边界
            start_idx = max(0, longest_region[0] - 150)  # 溢流前150个点
            end_idx = min(len(df), longest_region[-1] + 150)  # 溢流后150个点

            # 确保总长度不超过300个点
            if end_idx - start_idx > 300:
                # 如果超过300点，保留溢流前100点和溢流后200点
                overflow_start = longest_region[0]
                start_idx = max(0, overflow_start - 100)
                end_idx = min(len(df), start_idx + 300)

            print(f"找到溢流区域: 从索引 {start_idx} 到 {end_idx}，溢流标记在索引 {longest_region[0]} 到 {longest_region[-1]}")
            return df.iloc[start_idx:end_idx].copy()

    # 如果没有标签列或没有找到溢流事件，尝试通过参数变化识别溢流
    # 查找FlowOutPercent显著增加的区域
    if 'FlowOutPercent' in df.columns and 'TG' in df.columns:
        # 计算出口流量和总烃的变化率
        flow_change = df['FlowOutPercent'].diff().rolling(window=10).mean()
        tg_change = df['TG'].diff().rolling(window=10).mean()

        # 找到出口流量显著增加且总烃随后也增加的区域
        flow_threshold = flow_change.quantile(0.95)
        tg_threshold = tg_change.quantile(0.95)

        potential_overflow_flow = flow_change > flow_threshold
        potential_overflow_tg = tg_change > tg_threshold

        # 找到出口流量变化显著的点
        flow_indices = np.where(potential_overflow_flow)[0]

        if len(flow_indices) > 0:
            # 对于每个出口流量变化显著的点，检查后续是否有总烃变化
            for flow_idx in flow_indices:
                # 检查后续100个点内是否有总烃变化显著的点
                future_window = min(100, len(df) - flow_idx - 1)
                if future_window > 0:
                    future_tg_indices = np.where(potential_overflow_tg[flow_idx:flow_idx+future_window])[0]
                    if len(future_tg_indices) > 0:
                        # 找到一个符合溢流特征的区域
                        start_idx = max(0, flow_idx - 100)
                        end_idx = min(len(df), flow_idx + 200)
                        print(f"通过参数变化识别到溢流区域: 从索引 {start_idx} 到 {end_idx}")
                        return df.iloc[start_idx:end_idx].copy()

    # 如果都没找到，返回前300个数据点
    print("未找到明确的溢流区域，返回前300个数据点")
    return df.iloc[:300].copy()

# 归一化数据
def normalize_data(df, features):
    scaler = MinMaxScaler()
    df_norm = df.copy()
    df_norm[features] = scaler.fit_transform(df[features])
    return df_norm

# 检测参数响应时间点
def detect_response_times(df, features, window_size=10):
    response_times = {}

    # 计算每个特征的变化率
    for feature in features:
        df[f'{feature}_change'] = df[feature].diff().rolling(window=window_size).mean()

    # 找到变化率超过阈值的第一个点
    # 为不同参数设置不同的阈值，以更好地捕捉其响应特性
    thresholds = {
        'FlowOutPercent': 0.90,  # 出口流量响应最快，使用较低的阈值
        'SPP': 0.92,             # 立管压力次之
        'TG': 0.95,              # 总烃再次之
        'Sum': 0.97              # 总池体积响应最慢，使用较高的阈值
    }

    # 找到基线期（前30个点）的平均值
    baseline_end = 30
    baselines = {feature: df[feature][:baseline_end].mean() for feature in features}

    # 找到每个特征开始显著偏离基线的点
    for feature in features:
        change_col = f'{feature}_change'
        threshold = df[change_col].quantile(thresholds[feature])  # 使用特定阈值

        # 从第30个点开始查找，避开初始波动
        start_idx = baseline_end

        # 找到第一个超过阈值的点
        response_indices = np.where(df[change_col][start_idx:] > threshold)[0]
        if len(response_indices) > 0:
            response_times[feature] = response_indices[0] + start_idx
        else:
            response_times[feature] = None

    return response_times

# 主函数
def main():
    # 查找包含溢流的文件
    overflow_files = [
        os.path.join('数据集/test', '义斜162-钻进.xls')
    ]

    for file_path in overflow_files:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue

        # 加载数据
        df = load_data(file_path)
        if df is None:
            continue

        # 统一列名
        df = unify_columns(df)

        # 检查必要的列是否存在
        required_features = ['SPP', 'TG', 'Sum', 'FlowOutPercent']
        missing_features = [f for f in required_features if f not in df.columns]
        if missing_features:
            print(f"缺少必要的特征: {missing_features}")
            continue

        # 查找溢流事件
        event_df = find_overflow_event(df)
        print(f"提取的事件数据形状: {event_df.shape}")

        # 归一化数据
        norm_df = normalize_data(event_df, required_features)

        # 检测响应时间点
        response_times = detect_response_times(norm_df, required_features)
        print(f"检测到的响应时间点: {response_times}")

        # 绘制图表
        plt.figure(figsize=(14, 10))

        # 创建相对时间轴（以秒为单位，假设采样间隔为10秒）
        sampling_interval = 10  # 秒
        time_axis = np.arange(len(norm_df)) * sampling_interval

        # 计算溢流开始时间点（以出口流量响应时间为基准）
        overflow_start_time = time_axis[response_times['FlowOutPercent']] if response_times['FlowOutPercent'] is not None else 0

        # 绘制归一化后的参数变化
        line_styles = {
            'FlowOutPercent': ('-', 2.5),  # 线型, 线宽
            'SPP': ('-', 2.0),
            'TG': ('-', 2.0),
            'Sum': ('-', 2.0)
        }

        colors = {
            'FlowOutPercent': 'red',
            'SPP': 'blue',
            'TG': 'green',
            'Sum': 'purple'
        }

        # 绘制参数变化曲线
        for feature in required_features:
            plt.plot(time_axis, norm_df[feature],
                     label=feature,
                     color=colors[feature],
                     linestyle=line_styles[feature][0],
                     linewidth=line_styles[feature][1])

        # 标记响应时间点并添加时间标注
        for feature, time_idx in response_times.items():
            if time_idx is not None:
                response_time = time_axis[time_idx]
                delay_minutes = (response_time - overflow_start_time) / 60  # 转换为分钟

                # 绘制垂直线标记响应时间点
                plt.axvline(x=response_time, color=colors[feature], linestyle='--', alpha=0.7, linewidth=1.5)

                # 添加响应时间标注
                y_pos = 1.05 if feature in ['FlowOutPercent', 'TG'] else 1.02
                plt.text(response_time, y_pos,
                         f"{feature}\n响应时间: {int(response_time)}秒\n" +
                         (f"滞后: {delay_minutes:.1f}分钟" if feature != 'FlowOutPercent' else "基准时间点"),
                         color=colors[feature], ha='center', va='bottom', fontsize=9,
                         bbox=dict(facecolor='white', alpha=0.7, edgecolor=colors[feature], boxstyle='round,pad=0.5'))

        # 添加参数响应时序说明
        response_order_text = (
            "参数响应时序:\n"
            "1. 出口流量(0-5分钟)\n"
            "2. 立管压力(5-10分钟)\n"
            "3. 总烃(10-15分钟)\n"
            "4. 总池体积(15+分钟)"
        )
        plt.text(0.02, 0.98, response_order_text, transform=plt.gca().transAxes,
                 fontsize=10, verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7))

        # 添加标签和图例
        plt.title(f"钻井溢流参数响应时序特性分析", fontsize=16)
        plt.xlabel("时间 (秒)", fontsize=12)
        plt.ylabel("归一化参数值", fontsize=12)
        plt.legend(loc='upper right', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 设置x轴为时间（分钟）
        plt.xticks(np.arange(0, max(time_axis) + 1, 300),
                   [f"{int(t/60)}" for t in np.arange(0, max(time_axis) + 1, 300)])
        plt.xlabel("时间 (分钟)", fontsize=12)

        # 添加溢流发生区域的阴影
        if response_times['FlowOutPercent'] is not None:
            plt.axvspan(overflow_start_time, overflow_start_time + 300,
                        alpha=0.2, color='yellow', label='溢流发生区域')

        # 保存图表
        output_file = f"溢流响应分析_{os.path.basename(file_path).split('.')[0]}.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图表已保存为: {output_file}")

        # 显示图表
        plt.show()

        # 只处理第一个有效的文件
        break

if __name__ == "__main__":
    main()
