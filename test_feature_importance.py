#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征重要性分析是否能正确反映权重差异
"""

import sys
import os
import torch
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型和分析函数
from Bi_LSTM import DrillingModel, FINAL_FEATURES, analyze_feature_importance

def test_feature_importance_analysis():
    """测试特征重要性分析"""
    print("测试特征重要性分析...")
    print(f"使用的特征列表: {FINAL_FEATURES}")
    
    # 创建模型实例
    input_size = len(FINAL_FEATURES)
    model = DrillingModel(input_size=input_size)
    
    # 设置为评估模式
    model.eval()
    
    # 检查是否有Captum库
    try:
        import captum
        print("✓ Captum库可用")
        
        # 进行特征重要性分析
        print("\n开始特征重要性分析...")
        importance_dict = analyze_feature_importance(model, FINAL_FEATURES, device='cpu')
        
        if importance_dict is not None:
            print("\n✓ 特征重要性分析成功完成")
            
            # 按重要性排序显示结果
            sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
            print("\n特征重要性排序（从高到低）:")
            for i, (feature, importance) in enumerate(sorted_features, 1):
                print(f"{i:2d}. {feature:20s}: {importance:.6f}")
                
            # 检查FlowOutPercent是否排在前列
            flowout_rank = next((i for i, (name, _) in enumerate(sorted_features, 1) if name == 'FlowOutPercent'), None)
            if flowout_rank and flowout_rank <= 3:
                print(f"\n✓ FlowOutPercent排在第{flowout_rank}位，符合预期（权重最高）")
            else:
                print(f"\n⚠ FlowOutPercent排在第{flowout_rank}位，可能需要调整")
                
            # 检查SPP是否排在后列
            spp_rank = next((i for i, (name, _) in enumerate(sorted_features, 1) if name == 'SPP'), None)
            if spp_rank and spp_rank >= 6:
                print(f"✓ SPP排在第{spp_rank}位，符合预期（权重最低）")
            else:
                print(f"⚠ SPP排在第{spp_rank}位，可能需要调整")
        else:
            print("✗ 特征重要性分析失败")
            
    except ImportError:
        print("✗ Captum库不可用，无法进行特征重要性分析")
        print("请安装Captum库: pip install captum")

if __name__ == "__main__":
    test_feature_importance_analysis()
