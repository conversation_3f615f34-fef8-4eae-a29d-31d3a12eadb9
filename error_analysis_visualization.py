#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误样本分析可视化工具

该脚本用于分析模型预测错误的样本，并生成可视化图表，帮助理解模型的错误模式。
主要功能：
1. 加载错误分析结果文件
2. 生成不同类型样本的特征均值比较图表
3. 生成不同类型样本的特征变化趋势比较图表
4. 分析关键区分特征
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def load_error_analysis_data(file_path='error_analysis.json'):
    """加载错误分析数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载错误分析数据: {file_path}")
        return data
    except Exception as e:
        print(f"加载错误分析数据失败: {str(e)}")
        return None

def plot_feature_means_comparison(data, save_path=None):
    """绘制不同类型样本的特征均值比较图表"""
    if not data:
        print("无数据可用于绘制特征均值比较图表")
        return
    
    # 提取数据
    categories = ['false_positives', 'false_negatives', 'correct_positives', 'correct_negatives']
    category_labels = ['误报', '漏报', '正确正例', '正确负例']
    
    # 检查数据格式
    if isinstance(data[categories[0]]['feature_means'], dict):
        # 字典格式
        features = list(data[categories[0]]['feature_means'].keys())
        feature_means = {
            cat: [data[cat]['feature_means'][feat] for feat in features]
            for cat in categories if cat in data
        }
    else:
        # 列表格式
        features = list(data['feature_names']) if 'feature_names' in data else [
            'FlowOutPercent', 'SPP', 'TG', 'Sum', 
            'FlowOut_Sum_Joint', 'FlowOut_SPP_Inverse', 
            'TG_FlowOut_Joint', 'FlowOut_Rate_Change', 
            'SPP_Decrease_Sustained', 'Multi_Param_Anomaly'
        ]
        feature_means = {
            cat: data[cat]['feature_means']
            for cat in categories if cat in data
        }
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置条形图的宽度和位置
    bar_width = 0.2
    index = np.arange(len(features))
    
    # 绘制条形图
    for i, (cat, label) in enumerate(zip(categories, category_labels)):
        if cat in feature_means:
            ax.bar(index + i*bar_width, feature_means[cat], bar_width, label=label)
    
    # 设置图表属性
    ax.set_xlabel('特征')
    ax.set_ylabel('均值')
    ax.set_title('不同类型样本的特征均值比较')
    ax.set_xticks(index + bar_width * (len(categories)-1)/2)
    ax.set_xticklabels(features, rotation=45, ha='right')
    ax.legend()
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"特征均值比较图表已保存为: {save_path}")
    
    # 显示图表
    plt.show()

def plot_feature_trends_comparison(data, save_path=None):
    """绘制不同类型样本的特征变化趋势比较图表"""
    if not data:
        print("无数据可用于绘制特征变化趋势比较图表")
        return
    
    # 提取数据
    categories = ['false_positives', 'false_negatives', 'correct_positives', 'correct_negatives']
    category_labels = ['误报', '漏报', '正确正例', '正确负例']
    
    # 检查数据格式和是否包含趋势数据
    has_trends = False
    for cat in categories:
        if cat in data and 'feature_trends' in data[cat]:
            has_trends = True
            break
    
    if not has_trends:
        print("数据中不包含特征变化趋势信息")
        return
    
    # 提取特征列表
    if isinstance(data[categories[0]]['feature_trends'], dict):
        # 字典格式
        features = list(data[categories[0]]['feature_trends'].keys())
        feature_trends = {
            cat: [data[cat]['feature_trends'][feat] for feat in features]
            for cat in categories if cat in data and 'feature_trends' in data[cat]
        }
    else:
        # 列表格式
        features = list(data['feature_names']) if 'feature_names' in data else [
            'FlowOutPercent', 'SPP', 'TG', 'Sum', 
            'FlowOut_Sum_Joint', 'FlowOut_SPP_Inverse', 
            'TG_FlowOut_Joint', 'FlowOut_Rate_Change', 
            'SPP_Decrease_Sustained', 'Multi_Param_Anomaly'
        ]
        feature_trends = {
            cat: data[cat]['feature_trends']
            for cat in categories if cat in data and 'feature_trends' in data[cat]
        }
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置条形图的宽度和位置
    bar_width = 0.2
    index = np.arange(len(features))
    
    # 绘制条形图
    for i, (cat, label) in enumerate(zip(categories, category_labels)):
        if cat in feature_trends:
            ax.bar(index + i*bar_width, feature_trends[cat], bar_width, label=label)
    
    # 设置图表属性
    ax.set_xlabel('特征')
    ax.set_ylabel('变化趋势(上升→)')
    ax.set_title('不同类型样本的特征变化趋势比较')
    ax.set_xticks(index + bar_width * (len(categories)-1)/2)
    ax.set_xticklabels(features, rotation=45, ha='right')
    ax.legend()
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"特征变化趋势比较图表已保存为: {save_path}")
    
    # 显示图表
    plt.show()

def analyze_key_features(data):
    """分析关键区分特征"""
    if not data or 'key_differentiating_features' not in data:
        print("无数据可用于分析关键区分特征")
        return
    
    # 提取关键特征数据
    key_features = data['key_differentiating_features']
    
    # 排序特征
    if isinstance(key_features, dict):
        sorted_features = sorted(key_features.items(), key=lambda x: x[1], reverse=True)
        feature_names = [item[0] for item in sorted_features]
        feature_scores = [item[1] for item in sorted_features]
    else:
        feature_names = list(data['feature_names']) if 'feature_names' in data else [
            'FlowOutPercent', 'SPP', 'TG', 'Sum', 
            'FlowOut_Sum_Joint', 'FlowOut_SPP_Inverse', 
            'TG_FlowOut_Joint', 'FlowOut_Rate_Change', 
            'SPP_Decrease_Sustained', 'Multi_Param_Anomaly'
        ]
        feature_scores = key_features
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    
    # 绘制水平条形图
    y_pos = np.arange(len(feature_names))
    plt.barh(y_pos, feature_scores, align='center')
    plt.yticks(y_pos, feature_names)
    plt.gca().invert_yaxis()  # 最重要的特征在顶部
    
    # 设置图表属性
    plt.xlabel('特征重要性分数')
    plt.title('特征重要性排序')
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.6)
    
    # 调整布局
    plt.tight_layout()
    
    # 显示图表
    plt.show()
    
    # 打印关键特征分析
    print("\n关键区分特征分析:")
    for name, score in zip(feature_names, feature_scores):
        print(f"- {name}: {score:.6f}")

def main():
    """主函数"""
    # 加载错误分析数据
    data = load_error_analysis_data()
    if not data:
        # 尝试加载另一个文件
        data = load_error_analysis_data('error_analysis_results.json')
        if not data:
            print("无法加载错误分析数据，请确保error_analysis.json或error_analysis_results.json文件存在")
            return
    
    # 绘制特征均值比较图表
    plot_feature_means_comparison(data, 'feature_means_comparison.png')
    
    # 绘制特征变化趋势比较图表
    plot_feature_trends_comparison(data, 'feature_trends_comparison.png')
    
    # 分析关键区分特征
    analyze_key_features(data)

if __name__ == "__main__":
    main()
