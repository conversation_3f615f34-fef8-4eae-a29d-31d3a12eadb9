"""
生成数据集描述统计信息脚本

此脚本调用Bi_LSTM.py中的generate_dataset_description函数，
生成详细的数据集描述统计信息，包括原始数据点数、序列样本数、
各数据集中溢流样本的数量和比例，以及SMOTE增强后的标签分布。

生成的描述将保存到dataset_description.txt文件中，
同时在控制台输出，方便用户直接复制到论文中使用。

用法:
    python generate_dataset_description.py
"""

import os
import sys

def main():
    """主函数，调用Bi_LSTM.py中的generate_dataset_description函数"""
    try:
        # 导入Bi_LSTM模块
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from Bi_LSTM import generate_dataset_description
        
        print("=" * 80)
        print("开始生成数据集描述统计信息...")
        print("=" * 80)
        
        # 调用函数生成描述
        description = generate_dataset_description()
        
        print("\n" + "=" * 80)
        print("数据集描述生成完成！")
        print("描述已保存到 'dataset_description.txt' 文件中")
        print("您可以直接复制以下内容到论文中使用：")
        print("=" * 80)
        print("\n" + description + "\n")
        
    except ImportError:
        print("错误：无法导入Bi_LSTM模块，请确保Bi_LSTM.py文件存在且可访问")
        return 1
    except Exception as e:
        print(f"生成数据集描述时出错：{str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
