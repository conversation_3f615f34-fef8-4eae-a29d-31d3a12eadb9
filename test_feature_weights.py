#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征权重设置是否正确
"""

import sys
import os
import torch
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型
from Bi_LSTM import DrillingModel, FINAL_FEATURES

def test_feature_weights():
    """测试特征权重是否正确设置"""
    print("测试特征权重设置...")
    print(f"使用的特征列表: {FINAL_FEATURES}")
    print(f"特征数量: {len(FINAL_FEATURES)}")
    
    # 创建模型实例
    input_size = len(FINAL_FEATURES)
    model = DrillingModel(input_size=input_size)
    
    # 获取特征权重
    if hasattr(model, 'feature_weights'):
        weights = model.feature_weights.detach().cpu().numpy()
        print("\n当前模型中的特征权重:")
        for i, (feature_name, weight) in enumerate(zip(FINAL_FEATURES, weights)):
            print(f"- {feature_name}: {weight:.4f}")
        
        # 验证权重是否有差异
        unique_weights = np.unique(np.round(weights, 2))
        print(f"\n权重的唯一值: {unique_weights}")
        
        if len(unique_weights) > 1:
            print("✓ 特征权重设置成功，存在差异化权重")
            
            # 找出权重最高和最低的特征
            max_idx = np.argmax(weights)
            min_idx = np.argmin(weights)
            print(f"权重最高的特征: {FINAL_FEATURES[max_idx]} ({weights[max_idx]:.4f})")
            print(f"权重最低的特征: {FINAL_FEATURES[min_idx]} ({weights[min_idx]:.4f})")
        else:
            print("✗ 特征权重设置失败，所有权重相同")
    else:
        print("✗ 模型中没有feature_weights属性")

if __name__ == "__main__":
    test_feature_weights()
