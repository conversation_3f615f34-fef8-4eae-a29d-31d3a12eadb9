import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt

# 定义工具函数，移到最前面来解决"未定义"错误
def prepare_model_file():
    """将Bi-LSTM.py复制为Bi_LSTM.py以便导入"""
    source = os.path.join(os.path.dirname(__file__), "Bi_LSTM.py")
    target = os.path.join(os.path.dirname(__file__), "Bi_LSTM.py")
    
    if os.path.exists(source) and not os.path.exists(target):
        import shutil
        shutil.copy2(source, target)
        print(f"已创建模型文件副本: {target}")

# 添加依赖检查
missing_dependencies = []
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QLabel, QFileDialog, QTabWidget, QGroupBox, 
                            QProgressBar, QComboBox, QSpinBox, QDoubleSpinBox, 
                            QTextEdit, QMessageBox, QCheckBox, QSlider)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal
    from PyQt5.QtGui import QFont
except ImportError:
    missing_dependencies.append("PyQt5")

# 尝试导入matplotlib Qt后端
try:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
except ImportError:
    missing_dependencies.append("matplotlib-qt")

# 检查是否有缺失依赖
if missing_dependencies:
    print("缺少以下依赖库，请安装后再运行：")
    for dep in missing_dependencies:
        print(f" - {dep}")
    print("\n请使用以下命令安装所需依赖：")
    print("pip install PyQt5")
    print("pip install matplotlib")
    sys.exit(1)

# 导入模型相关代码
try:
    # 确保Bi_LSTM.py文件存在
    if not os.path.exists(os.path.join(os.path.dirname(__file__), "Bi_LSTM.py")):
        # 创建Bi_LSTM.py副本
        prepare_model_file()
    
    # 修改导入语句，使其与原始Bi-LSTM.py中的类定义匹配
    from Bi_LSTM import (DrillingModel, unify_columns, calculate_DFO, 
                      preprocess_features, load_data, create_sequences, generate_labels)
    
    # 自定义DrillingDataset类，以避免导入错误
    class DrillingDataset(torch.utils.data.Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)
            self.y = torch.LongTensor(y)
            
        def __len__(self):
            return len(self.X)
        
        def __getitem__(self, idx):
            return self.X[idx], self.y[idx]
    
except ImportError as e:
    print(f"无法导入模型代码: {str(e)}")
    print("请确保Bi_LSTM.py文件存在且可访问")
    print("尝试修复中...")
    
    # 尝试获取更详细的错误信息
    import traceback
    traceback.print_exc()
    
    sys.exit(1)


class MatplotlibCanvas(FigureCanvas):
    """用于显示matplotlib图表的画布"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)


class TrainingThread(QThread):
    """训练模型的线程"""
    update_progress = pyqtSignal(int)
    update_metrics = pyqtSignal(dict)
    training_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, X_train, y_train, X_test, y_test, input_size, params):
        super().__init__()
        self.X_train = X_train
        self.y_train = y_train
        self.X_test = X_test 
        self.y_test = y_test
        self.input_size = input_size
        self.params = params
        
    def run(self):
        try:
            import torch.nn as nn
            import torch.optim as optim
            from torch.utils.data import DataLoader
            
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
            # 创建数据加载器
            train_dataset = DrillingDataset(self.X_train, self.y_train)
            test_dataset = DrillingDataset(self.X_test, self.y_test)
            
            batch_size = self.params.get('batch_size', 64)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
            
            # 创建模型
            model = DrillingModel(input_size=self.input_size).to(device)
            
            # 处理类别不平衡
            label_counts = np.bincount(self.y_train)
            class_weights = torch.tensor([
                1.0,
                label_counts[0]/label_counts[1] * self.params.get('class_weight_factor', 2.0)
            ], dtype=torch.float32).to(device)
            
            criterion = nn.CrossEntropyLoss(weight=class_weights)
            lr = self.params.get('learning_rate', 5e-4)
            optimizer = optim.AdamW(model.parameters(), lr=lr, 
                                   weight_decay=self.params.get('weight_decay', 1e-5))
            
            epochs = self.params.get('epochs', 100)
            patience = self.params.get('patience', 10)
            clip_grad_norm = self.params.get('clip_grad_norm', 1.0)
            
            # 训练历史记录
            history = {'train_loss': [], 'train_acc': [], 'test_acc': []}
            best_test_acc = 0
            best_epoch = 0
            no_improve_epochs = 0
            
            for epoch in range(epochs):
                model.train()
                total_loss = 0
                correct = 0
                total = 0
                
                for inputs, labels in train_loader:
                    inputs = inputs.to(device).float()
                    labels = labels.to(device).long()
                    
                    optimizer.zero_grad()
                    
                    outputs = model(inputs).float()
                    loss = criterion(outputs, labels)
                    
                    if torch.isnan(loss).any():
                        continue
                        
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)
                    optimizer.step()
                    
                    total_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    total += labels.size(0)
                    correct += (predicted == labels).sum().item()
                
                if total > 0:
                    avg_loss = total_loss / len(train_loader)
                    train_acc = correct / total
                    history['train_loss'].append(avg_loss)
                    history['train_acc'].append(train_acc)
                else:
                    avg_loss = float('nan')
                    train_acc = 0
                
                # 评估测试集
                model.eval()
                test_correct = 0
                test_total = 0
                
                with torch.no_grad():
                    for inputs, labels in test_loader:
                        inputs = inputs.to(device).float()
                        labels = labels.to(device).long()
                        
                        outputs = model(inputs)
                        _, predicted = torch.max(outputs.data, 1)
                        
                        test_total += labels.size(0)
                        test_correct += (predicted == labels).sum().item()
                
                test_acc = test_correct / test_total if test_total > 0 else 0
                history['test_acc'].append(test_acc)
                
                # 发送进度更新
                progress = int((epoch + 1) / epochs * 100)
                self.update_progress.emit(progress)
                
                # 发送指标更新
                metrics = {
                    'epoch': epoch + 1,
                    'train_loss': avg_loss,
                    'train_acc': train_acc,
                    'test_acc': test_acc
                }
                self.update_metrics.emit(metrics)
                
                # 保存最佳模型
                if test_acc > best_test_acc:
                    best_test_acc = test_acc
                    best_epoch = epoch
                    torch.save(model.state_dict(), "best_drilling_model.pth")
                    no_improve_epochs = 0
                else:
                    no_improve_epochs += 1
                
                # 早停
                if no_improve_epochs >= patience:
                    break
            
            # 加载最佳模型进行最终评估
            model.load_state_dict(torch.load("best_drilling_model.pth"))
            model.eval()
            
            true_labels = []
            pred_labels = []
            pred_probs = []
            
            with torch.no_grad():
                for inputs, labels in test_loader:
                    inputs = inputs.to(device).float()
                    outputs = model(inputs)
                    probs = torch.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs.data, 1)
                    
                    true_labels.extend(labels.cpu().numpy())
                    pred_labels.extend(predicted.cpu().numpy())
                    pred_probs.extend(probs[:, 1].cpu().numpy())
            
            from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
            
            cm = confusion_matrix(true_labels, pred_labels)
            report = classification_report(true_labels, pred_labels, output_dict=True)
            
            fpr, tpr, _ = roc_curve(true_labels, pred_probs)
            roc_auc = auc(fpr, tpr)
            
            # 保存ROC曲线数据到CSV
            roc_data = pd.DataFrame({
                'fpr': fpr, 
                'tpr': tpr,
                'auc': [roc_auc] * len(fpr)
            })
            roc_data.to_csv('roc_data.csv', index=False)
            
            # 训练结束，发送最终结果
            results = {
                'history': history,
                'best_epoch': best_epoch,
                'best_acc': best_test_acc,
                'confusion_matrix': cm,
                'classification_report': report,
                'roc_auc': roc_auc,
                'fpr': fpr,
                'tpr': tpr,
                'model_path': "best_drilling_model.pth"
            }
            
            self.training_finished.emit(results)
            
        except Exception as e:
            import traceback
            error_msg = f"训练出错: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)


class DrillingApp(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("钻井数据分析与溢流预测系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化数据存储变量
        self.df = None
        self.X = None 
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.used_features = []
        self.training_thread = None
        self.model = None
        self.training_results = None
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        
        # 创建选项卡
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # 数据准备选项卡
        self.create_data_tab()
        
        # 模型训练选项卡
        self.create_training_tab()
        
        # 模型评估选项卡
        self.create_evaluation_tab()
        
        # 预测选项卡
        self.create_prediction_tab()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_data_tab(self):
        data_tab = QWidget()
        self.tabs.addTab(data_tab, "数据准备")
        
        layout = QVBoxLayout()
        data_tab.setLayout(layout)
        
        # 数据加载部分
        data_group = QGroupBox("数据加载")
        data_layout = QVBoxLayout()
        data_group.setLayout(data_layout)
        
        # 选择数据集路径
        path_layout = QHBoxLayout()
        self.data_path_label = QLabel("数据集路径:")
        self.data_path_edit = QTextEdit()
        self.data_path_edit.setMaximumHeight(50)
        self.data_path_edit.setText(r"C:\Users\<USER>\Desktop\毕设\模型\数据集")
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_data_folder)
        path_layout.addWidget(self.data_path_label, 1)
        path_layout.addWidget(self.data_path_edit, 3)
        path_layout.addWidget(self.browse_btn, 1)
        data_layout.addLayout(path_layout)
        
        # 加载数据按钮
        self.load_data_btn = QPushButton("加载数据")
        self.load_data_btn.clicked.connect(self.load_dataset)
        data_layout.addWidget(self.load_data_btn)
        
        # 数据信息显示
        self.data_info = QTextEdit()
        self.data_info.setReadOnly(True)
        data_layout.addWidget(self.data_info)
        
        layout.addWidget(data_group)
        
        # 数据预处理部分
        preprocess_group = QGroupBox("数据预处理")
        preprocess_layout = QVBoxLayout()
        preprocess_group.setLayout(preprocess_layout)
        
        # 数据增强选项
        augment_layout = QHBoxLayout()
        augment_layout.addWidget(QLabel("样本不平衡处理:"))
        self.augment_checkbox = QCheckBox("自动处理样本不平衡")
        self.augment_checkbox.setChecked(True)
        augment_layout.addWidget(self.augment_checkbox)
        preprocess_layout.addLayout(augment_layout)
        
        # 序列长度设置
        seq_layout = QHBoxLayout()
        seq_layout.addWidget(QLabel("序列长度:"))
        self.seq_length = QSpinBox()
        self.seq_length.setRange(10, 100)
        self.seq_length.setValue(30)
        seq_layout.addWidget(self.seq_length)
        preprocess_layout.addLayout(seq_layout)
        
        # 测试集比例
        test_layout = QHBoxLayout()
        test_layout.addWidget(QLabel("测试集比例:"))
        self.test_ratio = QDoubleSpinBox()
        self.test_ratio.setRange(0.1, 0.5)
        self.test_ratio.setSingleStep(0.05)
        self.test_ratio.setValue(0.2)
        test_layout.addWidget(self.test_ratio)
        preprocess_layout.addLayout(test_layout)
        
        # 预处理按钮
        self.preprocess_btn = QPushButton("数据预处理")
        self.preprocess_btn.clicked.connect(self.preprocess_data)
        preprocess_layout.addWidget(self.preprocess_btn)
        
        # 预处理信息显示
        self.preprocess_info = QTextEdit()
        self.preprocess_info.setReadOnly(True)
        preprocess_layout.addWidget(self.preprocess_info)
        
        layout.addWidget(preprocess_group)
    
    def create_training_tab(self):
        training_tab = QWidget()
        self.tabs.addTab(training_tab, "模型训练")
        
        layout = QVBoxLayout()
        training_tab.setLayout(layout)
        
        # 训练参数部分
        params_group = QGroupBox("训练参数")
        params_layout = QVBoxLayout()
        params_group.setLayout(params_layout)
        
        # 批次大小
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("批次大小:"))
        self.batch_size = QSpinBox()
        self.batch_size.setRange(8, 256)
        self.batch_size.setSingleStep(8)
        self.batch_size.setValue(64)
        batch_layout.addWidget(self.batch_size)
        params_layout.addLayout(batch_layout)
        
        # 学习率
        lr_layout = QHBoxLayout()
        lr_layout.addWidget(QLabel("学习率:"))
        self.learning_rate = QDoubleSpinBox()
        self.learning_rate.setRange(0.00001, 0.01)
        self.learning_rate.setSingleStep(0.0001)
        self.learning_rate.setDecimals(6)
        self.learning_rate.setValue(0.0005)
        lr_layout.addWidget(self.learning_rate)
        params_layout.addLayout(lr_layout)
        
        # 权重衰减
        wd_layout = QHBoxLayout()
        wd_layout.addWidget(QLabel("权重衰减:"))
        self.weight_decay = QDoubleSpinBox()
        self.weight_decay.setRange(0.00001, 0.01)
        self.weight_decay.setSingleStep(0.00001)
        self.weight_decay.setDecimals(6)
        self.weight_decay.setValue(0.00001)
        wd_layout.addWidget(self.weight_decay)
        params_layout.addLayout(wd_layout)
        
        # 类别权重因子
        cw_layout = QHBoxLayout()
        cw_layout.addWidget(QLabel("类别权重因子:"))
        self.class_weight = QDoubleSpinBox()
        self.class_weight.setRange(1, 10)
        self.class_weight.setSingleStep(0.1)
        self.class_weight.setValue(2.0)
        cw_layout.addWidget(self.class_weight)
        params_layout.addLayout(cw_layout)
        
        # 轮次
        epoch_layout = QHBoxLayout()
        epoch_layout.addWidget(QLabel("训练轮次:"))
        self.epochs = QSpinBox()
        self.epochs.setRange(10, 500)
        self.epochs.setSingleStep(10)
        self.epochs.setValue(100)
        epoch_layout.addWidget(self.epochs)
        params_layout.addLayout(epoch_layout)
        
        # 早停耐心
        patience_layout = QHBoxLayout()
        patience_layout.addWidget(QLabel("早停轮次:"))
        self.patience = QSpinBox()
        self.patience.setRange(3, 30)
        self.patience.setValue(10)
        patience_layout.addWidget(self.patience)
        params_layout.addLayout(patience_layout)
        
        layout.addWidget(params_group)
        
        # 训练控制部分
        train_group = QGroupBox("训练控制")
        train_layout = QVBoxLayout()
        train_group.setLayout(train_layout)
        
        # 训练按钮
        self.train_btn = QPushButton("开始训练")
        self.train_btn.clicked.connect(self.start_training)
        train_layout.addWidget(self.train_btn)
        
        # 停止按钮
        self.stop_btn = QPushButton("停止训练")
        self.stop_btn.clicked.connect(self.stop_training)
        self.stop_btn.setEnabled(False)
        train_layout.addWidget(self.stop_btn)
        
        # 训练进度条
        self.progress_bar = QProgressBar()
        train_layout.addWidget(self.progress_bar)
        
        # 训练信息
        self.training_info = QTextEdit()
        self.training_info.setReadOnly(True)
        train_layout.addWidget(self.training_info)
        
        layout.addWidget(train_group)
        
        # 训练图表
        chart_group = QGroupBox("训练进度")
        chart_layout = QVBoxLayout()
        chart_group.setLayout(chart_layout)
        
        self.training_canvas = MatplotlibCanvas(width=5, height=4, dpi=100)
        chart_layout.addWidget(self.training_canvas)
        
        layout.addWidget(chart_group)
    
    def create_evaluation_tab(self):
        eval_tab = QWidget()
        self.tabs.addTab(eval_tab, "模型评估")
        
        layout = QVBoxLayout()
        eval_tab.setLayout(layout)
        
        # 结果部分
        results_group = QGroupBox("评估结果")
        results_layout = QHBoxLayout()
        results_group.setLayout(results_layout)
        
        # 左侧：指标和混淆矩阵
        metrics_layout = QVBoxLayout()
        
        # 指标文本
        self.metrics_text = QTextEdit()
        self.metrics_text.setReadOnly(True)
        metrics_layout.addWidget(QLabel("分类评估指标:"))
        metrics_layout.addWidget(self.metrics_text)
        
        # 混淆矩阵图
        self.cm_canvas = MatplotlibCanvas(width=4, height=4, dpi=100)
        metrics_layout.addWidget(QLabel("混淆矩阵:"))
        metrics_layout.addWidget(self.cm_canvas)
        
        results_layout.addLayout(metrics_layout)
        
        # 右侧：ROC曲线
        roc_layout = QVBoxLayout()
        self.roc_canvas = MatplotlibCanvas(width=4, height=4, dpi=100)
        roc_layout.addWidget(QLabel("ROC曲线:"))
        roc_layout.addWidget(self.roc_canvas)
        
        results_layout.addLayout(roc_layout)
        
        layout.addWidget(results_group)
        
        # 保存结果按钮
        self.save_results_btn = QPushButton("保存评估结果")
        self.save_results_btn.clicked.connect(self.save_evaluation_results)
        layout.addWidget(self.save_results_btn)
    
    def create_prediction_tab(self):
        pred_tab = QWidget()
        self.tabs.addTab(pred_tab, "模型预测")
        
        layout = QVBoxLayout()
        pred_tab.setLayout(layout)
        
        # 加载模型
        model_group = QGroupBox("模型加载")
        model_layout = QHBoxLayout()
        model_group.setLayout(model_layout)
        
        self.model_path = QTextEdit()
        self.model_path.setReadOnly(True)
        self.model_path.setMaximumHeight(30)
        self.model_path.setText("best_drilling_model.pth")
        
        self.load_model_btn = QPushButton("加载模型")
        self.load_model_btn.clicked.connect(self.load_model)
        
        model_layout.addWidget(QLabel("模型路径:"))
        model_layout.addWidget(self.model_path, 3)
        model_layout.addWidget(self.load_model_btn, 1)
        
        layout.addWidget(model_group)
        
        # 预测数据
        data_group = QGroupBox("预测数据")
        data_layout = QVBoxLayout()
        data_group.setLayout(data_layout)
        
        # 数据选择
        select_layout = QHBoxLayout()
        self.pred_data_path = QTextEdit()
        self.pred_data_path.setMaximumHeight(30)
        self.browse_pred_btn = QPushButton("浏览...")
        self.browse_pred_btn.clicked.connect(self.browse_prediction_file)
        
        select_layout.addWidget(QLabel("预测数据文件:"))
        select_layout.addWidget(self.pred_data_path, 3)
        select_layout.addWidget(self.browse_pred_btn, 1)
        
        data_layout.addLayout(select_layout)
        
        # 加载预测数据
        self.load_pred_data_btn = QPushButton("加载预测数据")
        self.load_pred_data_btn.clicked.connect(self.load_prediction_data)
        data_layout.addWidget(self.load_pred_data_btn)
        
        # 预测数据信息
        self.pred_data_info = QTextEdit()
        self.pred_data_info.setReadOnly(True)
        data_layout.addWidget(self.pred_data_info)
        
        layout.addWidget(data_group)
        
        # 预测结果
        result_group = QGroupBox("预测结果")
        result_layout = QVBoxLayout()
        result_group.setLayout(result_layout)
        
        # 执行预测按钮
        self.predict_btn = QPushButton("执行预测")
        self.predict_btn.clicked.connect(self.run_prediction)
        result_layout.addWidget(self.predict_btn)
        
        # 预测结果展示
        self.prediction_canvas = MatplotlibCanvas(width=5, height=4, dpi=100)
        result_layout.addWidget(self.prediction_canvas)
        
        # 预测结果信息
        self.prediction_info = QTextEdit()
        self.prediction_info.setReadOnly(True)
        result_layout.addWidget(self.prediction_info)
        
        # 保存预测结果按钮
        self.save_pred_btn = QPushButton("保存预测结果")
        self.save_pred_btn.clicked.connect(self.save_prediction_results)
        result_layout.addWidget(self.save_pred_btn)
        
        layout.addWidget(result_group)
    
    def browse_data_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择数据集文件夹")
        if folder:
            self.data_path_edit.setText(folder)
    
    def load_dataset(self):
        try:
            dataset_dir = self.data_path_edit.toPlainText().strip()
            
            if not os.path.exists(dataset_dir):
                QMessageBox.warning(self, "错误", f"路径不存在: {dataset_dir}")
                return
                
            # 修改DATASET_DIR全局变量
            import Bi_LSTM
            Bi_LSTM.DATASET_DIR = dataset_dir
            
            # 加载数据
            self.df = load_data()
            
            if len(self.df) == 0:
                QMessageBox.warning(self, "警告", "未加载到有效数据！")
                return
            
            # 更新信息
            info_text = f"成功加载数据:\n"
            info_text += f"- 数据大小: {self.df.shape[0]} 行 × {self.df.shape[1]} 列\n"
            info_text += f"- 溢流样本: {(self.df['Label']==1).sum()} 个 ({(self.df['Label']==1).sum()/len(self.df)*100:.2f}%)\n"
            info_text += f"- 正常样本: {(self.df['Label']==0).sum()} 个 ({(self.df['Label']==0).sum()/len(self.df)*100:.2f}%)\n"
            info_text += f"- 工况分布:\n"
            
            if 'WorkingCondition' in self.df.columns:
                cond_counts = self.df['WorkingCondition'].value_counts()
                for cond, count in cond_counts.items():
                    info_text += f"  · {cond}: {count} 个 ({count/len(self.df)*100:.2f}%)\n"
            
            # 获取有效特征列
            available_features = ['ROP', 'SPP', 'RPM', 'HKH', 'BITDEPTH', 'Sum', 'FlowOutPercent', 'TG']
            valid_features = [col for col in available_features if col in self.df.columns and self.df[col].abs().sum() > 0]
            
            info_text += f"- 有效特征: {', '.join(valid_features)}"
            
            self.data_info.setText(info_text)
            self.statusBar().showMessage("数据加载成功")
            
            # 启用预处理按钮
            self.preprocess_btn.setEnabled(True)
            
        except Exception as e:
            import traceback
            error_msg = f"数据加载失败: {str(e)}\n{traceback.format_exc()}"
            self.data_info.setText(error_msg)
            self.statusBar().showMessage("数据加载失败")
            QMessageBox.critical(self, "错误", f"数据加载失败: {str(e)}")
    
    def preprocess_data(self):
        try:
            if self.df is None or len(self.df) == 0:
                QMessageBox.warning(self, "警告", "请先加载数据!")
                return
            
            # 获取参数
            seq_length = self.seq_length.value()
            test_ratio = self.test_ratio.value()
            handle_imbalance = self.augment_checkbox.isChecked()
            
            # 数据标准化 - 动态确定特征列
            available_features = ['ROP', 'SPP', 'RPM', 'HKH', 'BITDEPTH', 'Sum', 'FlowOutPercent', 'TG']
            feature_cols = [col for col in available_features if col in self.df.columns and self.df[col].abs().sum() > 0]
            
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            self.df[feature_cols] = scaler.fit_transform(self.df[feature_cols])
            
            # 检查NaN值
            if self.df.isna().any().any():
                self.df = self.df.fillna(0)
            
            # 生成序列数据
            self.X, self.y, self.used_features = create_sequences(self.df, sequence_length=seq_length)
            self.input_size = len(self.used_features)
            
            info_text = f"数据预处理完成:\n"
            info_text += f"- 序列长度: {seq_length}\n"
            info_text += f"- 特征数量: {self.input_size}\n"
            info_text += f"- 使用特征: {', '.join(self.used_features)}\n"
            info_text += f"- 序列总数: {len(self.X)}\n"
            
            # 处理类别不平衡
            if handle_imbalance and np.sum(self.y == 1) / len(self.y) < 0.1:
                from sklearn.utils import resample
                
                minority_indices = np.where(self.y == 1)[0]
                majority_indices = np.where(self.y == 0)[0]
                
                X_minority = self.X[minority_indices]
                y_minority = self.y[minority_indices]
                X_majority = self.X[majority_indices]
                y_majority = self.y[majority_indices]
                
                target_samples = int(len(majority_indices) * 0.2)
                if len(minority_indices) < target_samples:
                    X_minority_resampled, y_minority_resampled = resample(
                        X_minority,
                        y_minority,
                        replace=True,
                        n_samples=target_samples,
                        random_state=42
                    )
                    
                    self.X = np.vstack((X_majority, X_minority_resampled))
                    self.y = np.hstack((y_majority, y_minority_resampled))
                    
                    info_text += f"- 样本不平衡处理: 少数类从 {len(minority_indices)} 过采样到 {target_samples}\n"
            
            # 划分训练集和测试集
            from sklearn.model_selection import train_test_split
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                self.X, self.y, test_size=test_ratio, random_state=42, stratify=self.y
            )
            
            info_text += f"- 训练集: {len(self.X_train)} 序列, 正样本: {np.sum(self.y_train == 1)}, 比例: {np.sum(self.y_train == 1)/len(self.y_train):.2%}\n"
            info_text += f"- 测试集: {len(self.X_test)} 序列, 正样本: {np.sum(self.y_test == 1)}, 比例: {np.sum(self.y_test == 1)/len(self.y_test):.2%}\n"
            
            self.preprocess_info.setText(info_text)
            self.statusBar().showMessage("数据预处理完成")
            
            # 启用训练按钮
            self.train_btn.setEnabled(True)
            
        except Exception as e:
            import traceback
            error_msg = f"数据预处理失败: {str(e)}\n{traceback.format_exc()}"
            self.preprocess_info.setText(error_msg)
            self.statusBar().showMessage("数据预处理失败")
            QMessageBox.critical(self, "错误", f"数据预处理失败: {str(e)}")
    
    def start_training(self):
        try:
            if self.X_train is None or self.y_train is None:
                QMessageBox.warning(self, "警告", "请先完成数据预处理!")
                return
            
            # 获取训练参数
            params = {
                'batch_size': self.batch_size.value(),
                'learning_rate': self.learning_rate.value(),
                'weight_decay': self.weight_decay.value(),
                'class_weight_factor': self.class_weight.value(),
                'epochs': self.epochs.value(),
                'patience': self.patience.value(),
                'clip_grad_norm': 1.0
            }
            
            # 创建训练线程
            self.training_thread = TrainingThread(
                self.X_train, self.y_train, 
                self.X_test, self.y_test,
                self.input_size, params
            )
            
            # 连接信号
            self.training_thread.update_progress.connect(self.update_progress)
            self.training_thread.update_metrics.connect(self.update_metrics)
            self.training_thread.training_finished.connect(self.training_finished)
            self.training_thread.error_occurred.connect(self.training_error)
            
            # 更新UI状态
            self.train_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
            self.training_info.clear()
            self.training_info.append("开始训练...")
            self.training_info.append(f"参数配置: 批次大小={params['batch_size']}, 学习率={params['learning_rate']}, "
                              f"权重衰减={params['weight_decay']}, 类别权重因子={params['class_weight_factor']}")
            self.statusBar().showMessage("训练中...")
            
            # 初始化训练图表
            self.training_canvas.axes.clear()
            self.training_canvas.axes.set_title("训练进度")
            self.training_canvas.axes.set_xlabel("轮次")
            self.training_canvas.axes.set_ylabel("指标值")
            self.training_canvas.fig.tight_layout()
            self.training_canvas.draw()
            
            # 启动训练线程
            self.training_thread.start()
            
        except Exception as e:
            import traceback
            error_msg = f"启动训练失败: {str(e)}\n{traceback.format_exc()}"
            self.training_info.append(error_msg)
            self.statusBar().showMessage("启动训练失败")
            QMessageBox.critical(self, "错误", f"启动训练失败: {str(e)}")
    
    def stop_training(self):
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.terminate()
            self.training_info.append("训练已手动停止")
            self.statusBar().showMessage("训练已停止")
            self.train_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def update_metrics(self, metrics):
        epoch = metrics['epoch']
        train_loss = metrics['train_loss']
        train_acc = metrics['train_acc']
        test_acc = metrics['test_acc']
        
        self.training_info.append(f"轮次 {epoch}: 损失={train_loss:.4f}, 训练准确率={train_acc:.4f}, 测试准确率={test_acc:.4f}")
        
        # 更新图表
        self.update_training_chart()
    
    def update_training_chart(self):
        if not hasattr(self, 'train_loss_line'):
            # 首次更新，创建线对象
            self.training_canvas.axes.clear()
            self.train_loss_line, = self.training_canvas.axes.plot([], [], 'r-', label='训练损失')
            self.train_acc_line, = self.training_canvas.axes.plot([], [], 'b-', label='训练准确率')
            self.test_acc_line, = self.training_canvas.axes.plot([], [], 'g-', label='测试准确率')
            self.training_canvas.axes.legend()
            self.training_canvas.axes.set_title("训练进度")
            self.training_canvas.axes.set_xlabel("轮次")
            self.training_canvas.axes.set_ylabel("指标值")
        
        # 获取当前训练历史
        train_history = {'train_loss': [], 'train_acc': [], 'test_acc': []}
        
        # 从训练信息中提取历史数据
        for line in self.training_info.toPlainText().split('\n'):
            if line.startswith("轮次 "):
                try:
                    parts = line.split(": ")[1].split(", ")
                    loss = float(parts[0].split("=")[1])
                    train_acc = float(parts[1].split("=")[1])
                    test_acc = float(parts[2].split("=")[1])
                    
                    train_history['train_loss'].append(loss)
                    train_history['train_acc'].append(train_acc)
                    train_history['test_acc'].append(test_acc)
                except:
                    pass
        
        if not train_history['train_loss']:
            return
        
        # 更新数据
        epochs = list(range(1, len(train_history['train_loss'])+1))
        
        self.train_loss_line.set_data(epochs, train_history['train_loss'])
        self.train_acc_line.set_data(epochs, train_history['train_acc'])
        self.test_acc_line.set_data(epochs, train_history['test_acc'])
        
        # 自动调整坐标轴范围
        self.training_canvas.axes.relim()
        self.training_canvas.axes.autoscale_view()
        
        # 刷新图表
        self.training_canvas.draw()
    
    def training_finished(self, results):
        self.training_results = results
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.statusBar().showMessage("训练完成")
        
        best_epoch = results['best_epoch'] + 1
        best_acc = results['best_acc']
        
        self.training_info.append(f"\n训练完成! 最佳模型在第 {best_epoch} 轮, 测试准确率: {best_acc:.4f}")
        
        # 更新评估选项卡
        self.update_evaluation_tab()
        
        # 切换到评估选项卡
        self.tabs.setCurrentIndex(2)
        
        # 激活预测选项卡
        self.model_path.setText("best_drilling_model.pth")
        
    def training_error(self, error_msg):
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.statusBar().showMessage("训练出错")
        self.training_info.append("\n训练出错:")
        self.training_info.append(error_msg)
        QMessageBox.critical(self, "训练错误", error_msg)
    
    def update_evaluation_tab(self):
        if not self.training_results:
            return
        
        # 显示分类指标
        cm = self.training_results['confusion_matrix']
        report = self.training_results['classification_report']
        roc_auc = self.training_results['roc_auc']
        
        metrics_text = f"ROC AUC: {roc_auc:.4f}\n\n"
        metrics_text += "分类报告:\n"
        
        # 添加精度、召回率和F1分数
        metrics_text += f"精度 (Precision):\n"
        metrics_text += f"- 类别 0: {report['0']['precision']:.4f}\n"
        metrics_text += f"- 类别 1: {report['1']['precision']:.4f}\n\n"
        
        metrics_text += f"召回率 (Recall):\n"
        metrics_text += f"- 类别 0: {report['0']['recall']:.4f}\n"
        metrics_text += f"- 类别 1: {report['1']['recall']:.4f}\n\n"
        
        metrics_text += f"F1分数:\n"
        metrics_text += f"- 类别 0: {report['0']['f1-score']:.4f}\n"
        metrics_text += f"- 类别 1: {report['1']['f1-score']:.4f}\n\n"
        
        metrics_text += f"总体准确率: {report['accuracy']:.4f}\n"
        metrics_text += f"加权F1分数: {report['weighted avg']['f1-score']:.4f}"
        
        self.metrics_text.setText(metrics_text)
        
        # 绘制混淆矩阵
        self.cm_canvas.axes.clear()
        im = self.cm_canvas.axes.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        self.cm_canvas.axes.set_title("混淆矩阵")
        self.cm_canvas.axes.set_ylabel('真实标签')
        self.cm_canvas.axes.set_xlabel('预测标签')
        self.cm_canvas.axes.set_xticks([0, 1])
        self.cm_canvas.axes.set_yticks([0, 1])
        self.cm_canvas.axes.set_xticklabels(['正常', '溢流'])
        self.cm_canvas.axes.set_yticklabels(['正常', '溢流'])
        
        # 在混淆矩阵中添加数字标注
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                self.cm_canvas.axes.text(j, i, f"{cm[i, j]}",
                             ha="center", va="center",
                             color="white" if cm[i, j] > thresh else "black")
                             
        self.cm_canvas.fig.tight_layout()
        self.cm_canvas.draw()
        
        # 绘制ROC曲线
        fpr = self.training_results['fpr']
        tpr = self.training_results['tpr']
        
        self.roc_canvas.axes.clear()
        self.roc_canvas.axes.plot(fpr, tpr, color='darkorange', lw=2, 
                label=f'ROC 曲线 (AUC = {roc_auc:.4f})')
        self.roc_canvas.axes.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')
        self.roc_canvas.axes.set_xlim([0.0, 1.0])
        self.roc_canvas.axes.set_ylim([0.0, 1.05])
        self.roc_canvas.axes.set_xlabel('假阳性率')
        self.roc_canvas.axes.set_ylabel('真阳性率')
        self.roc_canvas.axes.set_title('接收者操作特征曲线')
        self.roc_canvas.axes.legend(loc="lower right")
        self.roc_canvas.fig.tight_layout()
        self.roc_canvas.draw()
    
    def save_evaluation_results(self):
        if not self.training_results:
            QMessageBox.warning(self, "警告", "没有可用的评估结果!")
            return
            
        folder = QFileDialog.getExistingDirectory(self, "选择保存位置")
        if not folder:
            return
            
        try:
            # 保存混淆矩阵图
            cm_path = os.path.join(folder, "confusion_matrix.png")
            self.cm_canvas.fig.savefig(cm_path, dpi=300, bbox_inches='tight')
            
            # 保存ROC曲线图
            roc_path = os.path.join(folder, "roc_curve.png")
            self.roc_canvas.fig.savefig(roc_path, dpi=300, bbox_inches='tight')
            
            # 保存分类报告
            report_path = os.path.join(folder, "classification_report.txt")
            with open(report_path, 'w') as f:
                f.write(self.metrics_text.toPlainText())
            
            # 保存ROC数据
            roc_data_path = os.path.join(folder, "roc_data.csv")
            roc_data = pd.DataFrame({
                'fpr': self.training_results['fpr'],
                'tpr': self.training_results['tpr'],
                'auc': [self.training_results['roc_auc']] * len(self.training_results['fpr'])
            })
            roc_data.to_csv(roc_data_path, index=False)
            
            # 保存模型
            model_path = os.path.join(folder, "drilling_model.pth")
            import shutil
            shutil.copyfile("best_drilling_model.pth", model_path)
            
            QMessageBox.information(self, "成功", f"所有评估结果已保存到:\n{folder}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存评估结果失败: {str(e)}")
    
    def browse_prediction_file(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择预测数据文件", "", "Excel文件 (*.xls *.xlsx);;CSV文件 (*.csv)")
        if file:
            self.pred_data_path.setText(file)
    
    def load_model(self):
        try:
            model_path = self.model_path.toPlainText().strip()
            if not os.path.exists(model_path):
                QMessageBox.warning(self, "警告", f"模型文件不存在: {model_path}")
                return
            
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
            # 如果没有预处理数据，则无法确定input_size
            if not hasattr(self, 'input_size') or self.input_size is None:
                # 尝试从ROC数据中加载特征数量
                try:
                    roc_data = pd.read_csv('roc_data.csv')
                    self.input_size = 4  # 默认值
                except:
                    self.input_size = 4  # 使用默认值
            
            self.model = DrillingModel(input_size=self.input_size).to(device)
            self.model.load_state_dict(torch.load(model_path, map_location=device))
            self.model.eval()
            
            QMessageBox.information(self, "成功", "模型加载成功!")
            self.statusBar().showMessage("模型加载成功")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"模型加载失败: {str(e)}")
            self.statusBar().showMessage("模型加载失败")
    
    def load_prediction_data(self):
        try:
            file_path = self.pred_data_path.toPlainText().strip()
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                return
            
            # 根据文件类型加载数据
            if file_path.endswith('.csv'):
                self.pred_df = pd.read_csv(file_path)
            else:
                try:
                    self.pred_df = pd.read_excel(file_path)
                except:
                    self.pred_df = pd.read_excel(file_path, engine='openpyxl')
            
            # 预处理数据
            self.pred_df = unify_columns(self.pred_df)
            self.pred_df = calculate_DFO(self.pred_df)
            self.pred_df = preprocess_features(self.pred_df)
            self.pred_df = generate_labels(self.pred_df)
            
            # 显示数据信息
            info_text = f"预测数据加载成功:\n"
            info_text += f"- 数据大小: {self.pred_df.shape[0]} 行 × {self.pred_df.shape[1]} 列\n"
            
            if 'Label' in self.pred_df.columns:
                info_text += f"- 预设标签分布:\n"
                info_text += f"  · 标签0 (正常): {(self.pred_df['Label']==0).sum()} 个\n"
                info_text += f"  · 标签1 (溢流): {(self.pred_df['Label']==1).sum()} 个\n"
            
            if 'WorkingCondition' in self.pred_df.columns:
                info_text += f"- 工况分布:\n"
                cond_counts = self.pred_df['WorkingCondition'].value_counts()
                for cond, count in cond_counts.items():
                    info_text += f"  · {cond}: {count} 个\n"
            
            # 检查是否包含必要的特征
            available_features = ['ROP', 'SPP', 'RPM', 'HKH', 'BITDEPTH', 'Sum', 'FlowOutPercent', 'TG']
            valid_features = [col for col in available_features if col in self.pred_df.columns and self.pred_df[col].abs().sum() > 0]
            
            info_text += f"- 有效特征: {', '.join(valid_features)}"
            self.pred_data_info.setText(info_text)
            
            self.statusBar().showMessage("预测数据加载成功")
            
        except Exception as e:
            import traceback
            error_msg = f"加载预测数据失败: {str(e)}\n{traceback.format_exc()}"
            self.pred_data_info.setText(error_msg)
            QMessageBox.critical(self, "错误", f"加载预测数据失败: {str(e)}")
            self.statusBar().showMessage("预测数据加载失败")
    
    def run_prediction(self):
        try:
            if self.model is None:
                QMessageBox.warning(self, "警告", "请先加载模型!")
                return
                
            if not hasattr(self, 'pred_df') or self.pred_df is None:
                QMessageBox.warning(self, "警告", "请先加载预测数据!")
                return
            
            # 标准化特征
            available_features = ['ROP', 'SPP', 'RPM', 'HKH', 'BITDEPTH', 'Sum', 'FlowOutPercent', 'TG']
            feature_cols = [col for col in available_features if col in self.pred_df.columns and self.pred_df[col].abs().sum() > 0]
            
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            self.pred_df[feature_cols] = scaler.fit_transform(self.pred_df[feature_cols])
            
            # 检查NaN值
            if self.pred_df.isna().any().any():
                self.pred_df = self.pred_df.fillna(0)
            
            # 生成序列数据
            seq_length = 30  # 使用默认序列长度
            X_pred, y_true, used_features = create_sequences(self.pred_df, sequence_length=seq_length)
            
            # 预测
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
            self.model.eval()
            predictions = []
            probabilities = []
            
            with torch.no_grad():
                for i in range(0, len(X_pred), 64):  # 批次大小为64
                    batch = torch.FloatTensor(X_pred[i:i+64]).to(device)
                    outputs = self.model(batch)
                    probs = torch.softmax(outputs, dim=1)
                    _, preds = torch.max(outputs, 1)
                    
                    predictions.extend(preds.cpu().numpy())
                    probabilities.extend(probs[:, 1].cpu().numpy())  # 类别1的概率
            
            # 创建结果DataFrame
            results = pd.DataFrame({
                'Prediction': predictions,
                'Probability': probabilities
            })
            
            # 如果有真实标签，添加到结果中
            if len(y_true) > 0:
                results['True_Label'] = y_true[:len(predictions)]
            
            # 计算性能指标
            info_text = f"预测完成，共预测 {len(predictions)} 个样本:\n\n"
            
            if 'True_Label' in results.columns:
                from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
                
                accuracy = accuracy_score(results['True_Label'], results['Prediction'])
                precision = precision_score(results['True_Label'], results['Prediction'], zero_division=0)
                recall = recall_score(results['True_Label'], results['Prediction'], zero_division=0)
                f1 = f1_score(results['True_Label'], results['Prediction'], zero_division=0)
                
                info_text += f"性能指标:\n"
                info_text += f"- 准确率: {accuracy:.4f}\n"
                info_text += f"- 精确率: {precision:.4f}\n"
                info_text += f"- 召回率: {recall:.4f}\n"
                info_text += f"- F1分数: {f1:.4f}\n\n"
            
            # 溢流检测统计
            overflow_count = results['Prediction'].sum()
            overflow_percent = overflow_count / len(results) * 100
            
            info_text += f"溢流检测结果:\n"
            info_text += f"- 正常样本: {len(results) - overflow_count} 个 ({100 - overflow_percent:.2f}%)\n"
            info_text += f"- 溢流样本: {overflow_count} 个 ({overflow_percent:.2f}%)"
            
            self.prediction_info.setText(info_text)
            
            # 绘制预测结果
            self.prediction_canvas.axes.clear()
            
            # 添加概率图
            self.prediction_canvas.axes.plot(results['Probability'], 'b-', alpha=0.5, label='溢流概率')
            
            # 添加预测结果
            self.prediction_canvas.axes.plot(results['Prediction'], 'r-', linewidth=2, label='预测结果')
            
            # 如果有真实标签，也添加进去
            if 'True_Label' in results.columns:
                self.prediction_canvas.axes.plot(results['True_Label'], 'g--', alpha=0.7, label='真实标签')
            
            self.prediction_canvas.axes.set_title("溢流预测结果")
            self.prediction_canvas.axes.set_xlabel("样本索引")
            self.prediction_canvas.axes.set_ylabel("标签/概率")
            self.prediction_canvas.axes.set_ylim(-0.1, 1.1)
            self.prediction_canvas.axes.legend()
            self.prediction_canvas.fig.tight_layout()
            self.prediction_canvas.draw()
            
            # 保存预测结果
            self.prediction_results = results
            
            self.statusBar().showMessage("预测完成")
            
        except Exception as e:
            import traceback
            error_msg = f"执行预测失败: {str(e)}\n{traceback.format_exc()}"
            self.prediction_info.setText(error_msg)
            QMessageBox.critical(self, "错误", f"执行预测失败: {str(e)}")
            self.statusBar().showMessage("预测失败")
    
    def save_prediction_results(self):
        if not hasattr(self, 'prediction_results') or self.prediction_results is None:
            QMessageBox.warning(self, "警告", "没有可用的预测结果!")
            return
            
        file, _ = QFileDialog.getSaveFileName(self, "保存预测结果", "", "CSV文件 (*.csv);;Excel文件 (*.xlsx)")
        
        if not file:
            return
            
        try:
            # 保存预测结果
            if file.endswith('.csv'):
                self.prediction_results.to_csv(file, index=True)
            else:
                self.prediction_results.to_excel(file, index=True)
            
            # 保存图表
            fig_path = os.path.splitext(file)[0] + "_预测图.png"
            self.prediction_canvas.fig.savefig(fig_path, dpi=300, bbox_inches='tight')
            
            QMessageBox.information(self, "成功", f"预测结果已保存到:\n{file}\n\n预测图已保存到:\n{fig_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存预测结果失败: {str(e)}")


if __name__ == "__main__":
    # 准备模型文件
    prepare_model_file()
    
    # 启动应用
    app = QApplication(sys.argv)
    window = DrillingApp()
    window.show()
    sys.exit(app.exec_())
