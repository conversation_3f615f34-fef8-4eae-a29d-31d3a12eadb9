import os
import re
import sys  # 添加sys模块用于异常时退出
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler  # 使用StandardScaler替代MinMaxScaler
from imblearn.over_sampling import SMOTE  # 添加SMOTE导入

# 尝试导入Captum库用于模型可解释性
try:
    import captum
    from captum.attr import IntegratedGradients, LayerIntegratedGradients
    from captum.attr import visualization as viz
    CAPTUM_AVAILABLE = True
except ImportError:
    print("警告: 未找到Captum库，将不能使用特征重要性分析功能")
    CAPTUM_AVAILABLE = False

# 定义最终使用的特征列表
FINAL_FEATURES = [
    # 基本特征 - 四个关键参数
    'FlowOutPercent',       # 出口流量百分比 - 快响应参数
    'SPP',                  # 立管压力 - 次快响应参数
    'TG',                   # 总烃 - 次慢响应参数
    'Sum',                  # 总池体积 - 最慢响应参数

    # 优化的衍生特征 - 基于错误分析结果保留最有效的特征
    'FlowOut_Sum_Joint',    # 出口流量与总池体积协同变化 - 直接证据
    'FlowOut_Rate_Change',  # 出口流量变化率 - 突变检测
    'Multi_Param_Anomaly',  # 多参数异常协同指数 - 综合预警
    'TG_FlowOut_Joint',     # 总烃与出口流量协同变化 - 气侵识别

]


# 设置随机种子
def set_seed(seed=54):
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
set_seed()

#%% 路径配置
BASE_DIR = r"C:\Users\<USER>\Desktop\毕设\模型"
DATASET_DIR = os.path.join(BASE_DIR, "数据集")  # 数据集文件夹路径

#%% 列名标准化映射（根据图片参数命名更新）
COLUMN_MAP = {
    'SPP': ['SPP', 'SPP立压', '立压', '立管压力'],
    'FlowOut': ['FLOWOUT出口流量', 'FLOWOUT', '出口流量'],
    'Sum': ['Sum', 'PITTOTAL总池体积', 'PITTOTAL', '总池体积', '池体积'],
    'WOB': ['WOB', 'WOB钻压', '钻压'],
    'Torque': ['TORQUE', 'TORQUE扭矩', '扭矩'],
    'FlowOutPercent': ['FLOWOUTPERCENT出口流量百分比', 'FLOWOUTPERCENT', '出口流量百分比'],  # 出口流量百分比
    'TG': ['TG总烃', 'TG', '总烃']  # 总烃
}

# 定义窗口大小常量（用于特征处理）
WINDOW_SIZE = 10    # 基本窗口大小，约100秒

# 定义不同参数的响应窗口大小 - 基于10秒采样间隔调整
FLOWOUT_WINDOW = 30  # 出口流量响应最快 (0-5分钟 = 300秒 = 30个数据点)
SPP_WINDOW = 60      # 立管压力次之 (5-10分钟 = 600秒 = 60个数据点)
TG_WINDOW = 90       # 总烃再次之 (10-15分钟 = 900秒 = 90个数据点)
SUM_WINDOW = 120     # 总池体积最慢 (15分钟以上 = 1200秒 = 120个数据点)

# 定义滞后时间常量 - 基于10秒采样间隔调整
SHORT_LAG = 30       # 短期滞后 (约5分钟 = 300秒 = 30个数据点)
MEDIUM_LAG = 60      # 中期滞后 (约10分钟 = 600秒 = 60个数据点)
LONG_LAG = 90        # 长期滞后 (约15分钟 = 900秒 = 90个数据点)

#%% 统一列名处理（增强兼容性）
def unify_columns(df):
    """
    统一数据列名并调整列名格式，增强列名识别能力
    """
    # 保存原始标签（如果存在）
    original_label = None
    label_column = None

    # 检查是否有标签列
    for col in df.columns:
        if 'label' in col.lower() or '标签' in col.lower():
            label_column = col
            original_label = df[col].copy()
            print(f"列名统一前保存标签列: {col}")
            # 打印标签分布
            try:
                label_counts = df[col].value_counts().to_dict()
                print(f"原始标签分布: {label_counts}")
            except:
                print("无法显示原始标签分布")
            break

    original_columns = df.columns.tolist()  # 保存原始列名列表
    # 去除列名中的特殊字符和空格
    df.columns = [re.sub(r'[^\w]', '', col).strip() for col in df.columns]

    # 标准化列名映射
    for std_name, variants in COLUMN_MAP.items():
        for col in df.columns:
            # 更灵活的列名匹配
            if any(variant.lower() in col.lower() for variant in variants):
                df.rename(columns={col: std_name}, inplace=True)
                break

    # 添加必要的空列，如果不存在
    default_cols = ['SPP', 'Sum', 'FlowOutPercent', 'TG', 'Torque']
    for col in default_cols:
        if col not in df.columns:
            df[col] = 0
            print(f"警告：创建默认 {col} 列")

    # 恢复标签列（如果存在）
    if original_label is not None:
        # 如果标签列已经被映射为标准名称，则使用标准名称
        if label_column and label_column.lower() != 'label':
            df['Label'] = original_label
            print(f"将标签列 {label_column} 映射为标准名称 'Label'")
        else:
            # 如果标签列已经是'Label'，直接恢复
            df['Label'] = original_label
            print("恢复标准标签列 'Label'")

        # 打印恢复后的标签分布
        try:
            label_counts = df['Label'].value_counts().to_dict()
            print(f"恢复后标签分布: {label_counts}")
        except:
            print("无法显示恢复后的标签分布")

    # 打印列名映射信息
    print(f"列名映射: 原始 {len(original_columns)} 列 -> 映射后 {len(df.columns)} 列")
    return df

#%% 动态 DFO 计算
def calculate_DFO(df):
    """
    为了保持代码结构完整，保留此函数，但不使用DFO作为溢流判断特征
    """
    # 创建默认DFO列，但不使用它进行判断
    df['DFO'] = 0

    # 如果有FlowOut列，仍然计算DFO，但仅作为记录
    if 'FlowOut' in df.columns and df['FlowOut'].notna().any():
        try:
            df['DFO'] = df['FlowOut'].diff().abs().fillna(0)
            print("基于出口流量计算DFO成功(仅作记录)")
        except Exception as e:
            print(f"警告：计算DFO失败: {str(e)}")
    else:
        print("出口流量数据缺失，DFO 列仅作占位")

    return df

#%% 计算物理约束特征
def calculate_constraint_features(df, window_size=WINDOW_SIZE):

    print("计算基于物理机制的特征...")
    df_constrained = df.copy()

    # 确保所有基本特征列存在
    for col in ['SPP', 'Sum', 'FlowOutPercent', 'TG']:
        if col not in df_constrained.columns:
            df_constrained[col] = 0
            print(f"警告: 基本特征列 '{col}' 不存在，创建并填充0")

    # 1. 出口流量与总池体积协同变化特征 - 直接证据
    # 这个特征捕捉出口流量增加与总池体积同步增加的情况，是溢流的直接证据
    if 'FlowOutPercent' in df_constrained.columns and 'Sum' in df_constrained.columns:
        # 计算出口流量短期变化率 - 基于10秒采样间隔调整
        flow_change = df_constrained['FlowOutPercent'].pct_change(periods=FLOWOUT_WINDOW).fillna(0)
        # 计算总池体积长期变化率 - 基于10秒采样间隔调整
        sum_change = df_constrained['Sum'].pct_change(periods=SUM_WINDOW).fillna(0)

        # 当两者都为正时，乘积为正；一正一负或都为负时，乘积为负或较小
        # 使用滑动窗口平滑，减少噪声影响
        # 阈值调整：由于窗口变大，变化率阈值需要相应调整
        df_constrained['FlowOut_Sum_Joint'] = (
            (flow_change > 0.10) & (sum_change > 0.10)  # 调整阈值以适应更长的时间窗口
        ).rolling(window=FLOWOUT_WINDOW//2).mean().fillna(0)  # 平滑窗口调整为原窗口的一半

        # 增强信号 - 当变化率都较大时，信号更强
        df_constrained['FlowOut_Sum_Joint'] = df_constrained['FlowOut_Sum_Joint'] * (
            flow_change.abs() + sum_change.abs()
        )
    else:
        df_constrained['FlowOut_Sum_Joint'] = 0

    # 2. 出口流量上升与立管压力下降的反相关 - 强预警信号
    # 这个特征捕捉出口流量上升与立管压力下降的反相关关系，是溢流的强预警信号
    if 'FlowOutPercent' in df_constrained.columns and 'SPP' in df_constrained.columns:
        # 计算出口流量短期变化率 - 基于10秒采样间隔调整
        flow_change = df_constrained['FlowOutPercent'].pct_change(periods=FLOWOUT_WINDOW).fillna(0)
        # 计算立管压力中期变化率 - 基于10秒采样间隔调整
        spp_change = df_constrained['SPP'].pct_change(periods=SPP_WINDOW).fillna(0)

        # 当出口流量上升且立管压力下降时，这个特征值为正
        # 使用滑动窗口平滑，减少噪声影响
        # 阈值调整：由于窗口变大，变化率阈值需要相应调整
        df_constrained['FlowOut_SPP_Inverse'] = (
            (flow_change > 0.10) & (spp_change < -0.05)  # 调整阈值以适应更长的时间窗口
        ).rolling(window=SPP_WINDOW//3).mean().fillna(0)  # 平滑窗口调整为原窗口的三分之一

        # 增强信号 - 当变化率都较大时，信号更强
        df_constrained['FlowOut_SPP_Inverse'] = df_constrained['FlowOut_SPP_Inverse'] * (
            flow_change.abs() + spp_change.abs() * 2  # 增加SPP变化的权重
        )
    else:
        df_constrained['FlowOut_SPP_Inverse'] = 0

    # 3. 总烃与出口流量协同变化 - 气侵识别
    # 这个特征捕捉总烃上升与出口流量上升的协同关系，有助于区分气侵与其他流体侵入
    if 'TG' in df_constrained.columns and 'FlowOutPercent' in df_constrained.columns:
        # 计算总烃中期变化率 - 基于10秒采样间隔调整
        tg_change = df_constrained['TG'].pct_change(periods=TG_WINDOW).fillna(0)
        # 使用已计算的出口流量变化率 - 基于10秒采样间隔调整
        flow_change = df_constrained['FlowOutPercent'].pct_change(periods=FLOWOUT_WINDOW).fillna(0)

        # 考虑总烃响应滞后于出口流量的特性
        # 将出口流量向前移动SHORT_LAG个时间点，与总烃变化对齐
        flow_change_shifted = flow_change.shift(-SHORT_LAG).fillna(0)

        # 当总烃显著上升且出口流量上升时，这个特征值为正
        # 使用滑动窗口平滑，减少噪声影响
        # 阈值调整：由于窗口变大，变化率阈值需要相应调整
        df_constrained['TG_FlowOut_Joint'] = (
            (tg_change > 0.5) & (flow_change_shifted > 0.20)  # 调整阈值以适应更长的时间窗口
        ).rolling(window=TG_WINDOW//3).mean().fillna(0)  # 平滑窗口调整为原窗口的三分之一

        # 增强信号 - 当总烃变化率较大时，信号更强
        df_constrained['TG_FlowOut_Joint'] = df_constrained['TG_FlowOut_Joint'] * (
            tg_change.abs() * 2 + flow_change_shifted.abs()  # 增加总烃变化的权重
        )
    else:
        df_constrained['TG_FlowOut_Joint'] = 0

    # 4. 出口流量变化率 - 突变检测
    # 这个特征捕捉出口流量的突变，是溢流的早期信号
    if 'FlowOutPercent' in df_constrained.columns:
        # 计算短期变化率 - 基于10秒采样间隔调整
        short_change = df_constrained['FlowOutPercent'].pct_change(periods=FLOWOUT_WINDOW).fillna(0)
        # 计算更短期的变化率 - 使用10个数据点(约100秒)作为超短期窗口
        very_short_change = df_constrained['FlowOutPercent'].pct_change(periods=10).fillna(0)

        # 计算变化率的加速度 - 变化率的变化率
        # 使用滞后的短期变化率作为基准，检测突变
        df_constrained['FlowOut_Rate_Change'] = (
            very_short_change - short_change.shift(15)  # 使用15个数据点(约150秒)的滞后
        ).fillna(0)

        # 只关注正向突变（突然增加）
        # 增加阈值过滤，只保留显著的突变
        df_constrained['FlowOut_Rate_Change'] = df_constrained['FlowOut_Rate_Change'].apply(
            lambda x: x if x > 0.05 else 0  # 添加阈值过滤微小变化
        )

        # 使用指数平滑，突出大的变化
        df_constrained['FlowOut_Rate_Change'] = df_constrained['FlowOut_Rate_Change'] ** 2

        # 使用滑动窗口平滑，减少噪声影响，但保持窗口较小以不丢失突变信号
        df_constrained['FlowOut_Rate_Change'] = df_constrained['FlowOut_Rate_Change'].rolling(
            window=5  # 使用5个数据点(约50秒)的平滑窗口
        ).mean().fillna(0)
    else:
        df_constrained['FlowOut_Rate_Change'] = 0

    # 5. 立管压力持续下降 - 溢流持续性证据
    # 这个特征捕捉立管压力的持续下降，是溢流持续发展的证据
    if 'SPP' in df_constrained.columns:
        # 计算立管压力的滑动窗口线性回归斜率 - 基于10秒采样间隔调整
        df_constrained['SPP_Decrease_Sustained'] = df_constrained['SPP'].rolling(window=SPP_WINDOW).apply(
            lambda x: np.polyfit(np.arange(len(x)), x, 1)[0] if len(x) > 1 else 0
        ).fillna(0)

        # 只关注下降趋势（负斜率）
        df_constrained['SPP_Decrease_Sustained'] = df_constrained['SPP_Decrease_Sustained'].apply(
            lambda x: abs(x) if x < 0 else 0
        )

        # 计算持续性 - 连续下降的时间长度
        # 创建一个辅助列，标记每个点的压力是否低于前一个点
        decreasing = (df_constrained['SPP'].diff() < 0).astype(int)

        # 使用指数加权移动平均来平滑下降标记，减少噪声影响
        smoothed_decreasing = decreasing.ewm(span=10).mean()

        # 计算连续下降的长度 - 使用更长的窗口
        consecutive_decrease = smoothed_decreasing.rolling(window=SPP_WINDOW).sum().fillna(0)

        # 计算下降的持续时间比例
        duration_ratio = consecutive_decrease / SPP_WINDOW  # 归一化到0-1范围

        # 计算下降的幅度 - 窗口内的总变化百分比
        magnitude = abs(df_constrained['SPP'].pct_change(periods=SPP_WINDOW).fillna(0))

        # 将持续性因子、下降幅度和斜率相乘，综合考虑持续性和幅度
        df_constrained['SPP_Decrease_Sustained'] = (
            df_constrained['SPP_Decrease_Sustained'] *  # 斜率
            duration_ratio *                           # 持续时间比例
            (1 + magnitude)                            # 幅度增强因子
        )

        # 使用滑动窗口平滑最终结果
        df_constrained['SPP_Decrease_Sustained'] = df_constrained['SPP_Decrease_Sustained'].rolling(
            window=SPP_WINDOW//6  # 使用较小的平滑窗口
        ).mean().fillna(0)
    else:
        df_constrained['SPP_Decrease_Sustained'] = 0

    # 6. 多参数异常协同指数 - 综合预警
    # 这个特征综合考虑多个参数的异常情况，提供一个综合预警指数
    # 基于溢流判断规则：满足包含出口流量百分比增大的至少三条规则即判断为溢流

    # 计算各参数的异常得分 - 基于10秒采样间隔调整阈值
    # 出口流量异常 - 增加10%-50%
    flow_anomaly = (df_constrained['FlowOutPercent'].pct_change(periods=FLOWOUT_WINDOW) > 0.10).astype(float)

    # 立管压力异常 - 下降5%-15%
    spp_anomaly = (df_constrained['SPP'].pct_change(periods=SPP_WINDOW) < -0.05).astype(float)

    # 总烃异常 - 增加50%-200%
    tg_anomaly = (df_constrained['TG'].pct_change(periods=TG_WINDOW) > 0.50).astype(float)

    # 总池体积异常 - 增加10%-20%
    sum_anomaly = (df_constrained['Sum'].pct_change(periods=SUM_WINDOW) > 0.10).astype(float)

    # 使用滑动窗口平滑各个异常指标，减少瞬时波动影响
    flow_anomaly_smooth = flow_anomaly.rolling(window=FLOWOUT_WINDOW//3).mean().fillna(0)
    spp_anomaly_smooth = spp_anomaly.rolling(window=SPP_WINDOW//3).mean().fillna(0)
    tg_anomaly_smooth = tg_anomaly.rolling(window=TG_WINDOW//3).mean().fillna(0)
    sum_anomaly_smooth = sum_anomaly.rolling(window=SUM_WINDOW//3).mean().fillna(0)

    # 考虑参数响应时间差异，对异常信号进行时间对齐
    # 将较慢响应的参数向前移动，与出口流量对齐
    spp_anomaly_aligned = spp_anomaly_smooth.shift(-SHORT_LAG//2).fillna(0)  # 立管压力提前半个短期滞后
    tg_anomaly_aligned = tg_anomaly_smooth.shift(-SHORT_LAG).fillna(0)       # 总烃提前一个短期滞后
    sum_anomaly_aligned = sum_anomaly_smooth.shift(-MEDIUM_LAG).fillna(0)    # 总池体积提前一个中期滞后

    # 计算异常参数的加权和 - 根据物理意义赋予不同权重
    df_constrained['Multi_Param_Anomaly'] = (
        flow_anomaly_smooth * 1.5 +     # 出口流量权重最高
        spp_anomaly_aligned * 1.2 +     # 立管压力次之
        sum_anomaly_aligned * 1.0 +      # 总池体积再次
        tg_anomaly_aligned * 0.8       # 总烃最低
    )

    # 当出口流量不异常时，总分大幅降低
    # 使用滑动窗口检查一段时间内是否有出口流量异常
    flow_anomaly_window = flow_anomaly_smooth.rolling(window=FLOWOUT_WINDOW).max().fillna(0)
    df_constrained['Multi_Param_Anomaly'] = df_constrained['Multi_Param_Anomaly'] * (
        1.0 if flow_anomaly_window.any() else 0.3  # 更严格的惩罚
    )

    # 使用指数加权移动平均进行平滑，减少噪声影响同时保留近期信号的重要性
    df_constrained['Multi_Param_Anomaly'] = df_constrained['Multi_Param_Anomaly'].ewm(
        span=FLOWOUT_WINDOW//2  # 使用半个出口流量窗口作为平滑参数
    ).mean().fillna(0)

    # 归一化到0-1范围，便于解释
    max_val = df_constrained['Multi_Param_Anomaly'].max()
    if max_val > 0:
        df_constrained['Multi_Param_Anomaly'] = df_constrained['Multi_Param_Anomaly'] / max_val

    # 确保所有FINAL_FEATURES中的特征都存在
    for feature in FINAL_FEATURES:
        if feature not in df_constrained.columns:
            print(f"警告: 添加缺失的特征: {feature}")
            df_constrained[feature] = 0

    return df_constrained

#%% 特征预处理
def preprocess_features(df):
    """
    基本特征预处理，处理异常值和零值

    参数:
        df: 输入的DataFrame

    返回:
        预处理后的DataFrame
    """
    # 保存原始标签
    original_label = None
    if 'Label' in df.columns:
        print("预处理前保存原始标签")
        original_label = df['Label'].copy()
        # 打印标签分布
        label_counts = df['Label'].value_counts().to_dict()
        print(f"原始标签分布: {label_counts}")

    # 基本特征处理 - 处理异常值和零值
    # 处理四个关键参数的异常值
    for col in ['SPP', 'Sum', 'FlowOutPercent', 'TG']:
        if col in df.columns:
            # 检测并替换异常值 (使用3倍标准差作为阈值)
            mean_val = df[col].mean()
            std_val = df[col].std()
            lower_bound = mean_val - 3 * std_val
            upper_bound = mean_val + 3 * std_val

            # 将超出范围的值替换为边界值
            df[col] = df[col].clip(lower_bound, upper_bound)

            # 处理零值和极小值
            if col == 'SPP':
                # 立管压力零值处理 - 小于阈值的视为0
                df[col] = df[col].apply(lambda x: 0 if abs(x) <= 0.05 else x)
            elif col == 'TG':
                # 总烃零值处理 - 小于阈值的视为0
                df[col] = df[col].apply(lambda x: 0 if abs(x) <= 0.01 else x)

    # 处理缺失值
    if df.isna().any().any():
        print("警告: 发现缺失值，使用前向填充和后向填充")
        # 先尝试前向填充
        df = df.fillna(method='ffill')
        # 再尝试后向填充（处理开头的缺失值）
        df = df.fillna(method='bfill')
        # 最后使用0填充仍然存在的缺失值
        df = df.fillna(0)

    # 恢复原始标签
    if original_label is not None:
        print("预处理后恢复原始标签")
        df['Label'] = original_label
        # 打印恢复后的标签分布
        label_counts = df['Label'].value_counts().to_dict()
        print(f"恢复后标签分布: {label_counts}")

    return df

#%% 增强型数据加载器
def load_data():
    """
    从数据集文件夹加载数据，支持从train和test子文件夹分别加载

    返回:
        train_df: 训练集DataFrame
        test_df: 测试集DataFrame
        如果没有分开的train/test文件夹，则返回(combined_df, None)
    """
    train_dfs = []  # 存储训练集数据
    test_dfs = []   # 存储测试集数据
    all_dfs = []    # 存储所有数据（如果没有train/test子文件夹）

    successful_files = []
    skipped_files = []
    error_msgs = []

    # 检查是否有train和test子文件夹
    train_dir = os.path.join(DATASET_DIR, "train")
    test_dir = os.path.join(DATASET_DIR, "test")
    has_separate_folders = os.path.exists(train_dir) and os.path.exists(test_dir)

    if has_separate_folders:
        print("\n检测到train和test子文件夹，将分别加载数据")
        folders_to_process = [
            ("train", train_dir, train_dfs),
            ("test", test_dir, test_dfs)
        ]
    else:
        print("\n未检测到train和test子文件夹，将从主文件夹加载所有数据")
        folders_to_process = [("main", DATASET_DIR, all_dfs)]

    # 处理每个文件夹中的数据
    for folder_name, folder_path, target_dfs in folders_to_process:
        print(f"\n处理{folder_name}文件夹中的数据:")

        for root, _, files in os.walk(folder_path):
            for file in files:
                if not file.endswith(('.xls', '.xlsx')): continue

                print(f"\n尝试处理: {file}")
                try:
                    # 读取文件，处理可能的编码问题
                    filepath = os.path.join(root, file)
                    df = None  # 初始化DataFrame

                    # 多种方法尝试读取文件
                    methods = [
                        # 方法1: 使用openpyxl读取 .xlsx 文件
                        lambda: pd.read_excel(filepath, engine='openpyxl') if filepath.endswith('.xlsx') else None,

                        # 方法2: 尝试降级xlrd来读取 .xls 文件
                        lambda: pd.read_excel(filepath, engine='xlrd') if filepath.endswith('.xls') else None,

                        # 方法3: 不指定引擎，让pandas自动选择
                        lambda: pd.read_excel(filepath),

                        # 方法4: 使用xlrd自行处理 .xls 文件
                        lambda: handle_xls_file(filepath) if filepath.endswith('.xls') else None,
                    ]

                    # 依次尝试各种读取方法
                    last_error = None
                    for method_idx, method in enumerate(methods):
                        try:
                            if method_idx > 0:
                                print(f"尝试方法 {method_idx + 1} 读取文件...")
                            result = method()
                            if result is not None and isinstance(result, pd.DataFrame) and len(result) > 0:
                                df = result
                                break
                        except Exception as e:
                            last_error = str(e)
                            error_msgs.append(f"方法 {method_idx + 1} 读取失败: {str(e)}")
                            continue

                    if df is None or len(df) == 0:
                        raise Exception(f"所有读取方法均失败, 最后错误: {last_error}")

                    print(f"文件读取成功，原始数据形状: {df.shape}，列名: {df.columns.tolist()[:5]}...")

                    # 统一列名处理(需要先运行)
                    df = unify_columns(df)

                    # 生成必要的特征
                    df = calculate_DFO(df)
                    df = preprocess_features(df)

                    # 确保必需列存在（在计算约束特征前）
                    required_cols = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
                    for col in required_cols:
                        if col not in df.columns:
                            df[col] = 0 # 添加缺失列并填充0，避免后续计算出错
                            print(f"警告：添加缺失的必需列 {col} 并用0填充")
                        # 同时检查 trend 列是否存在，为后续计算准备
                        if f'{col}_trend' not in df.columns:
                            df[f'{col}_trend'] = 0

                    # ** 调用 calculate_constraint_features 计算新特征 **
                    print("计算物理约束相关特征...")
                    df = calculate_constraint_features(df, window_size=WINDOW_SIZE) # 使用全局窗口大小

                    # 不再考虑工况影响
                    print("不考虑工况影响")

                    # 检查label列
                    if 'label' in df.columns:
                        df.rename(columns={'label': 'Label'}, inplace=True)
                    if 'Label' not in df.columns:
                        print(f"跳过文件 {file}，缺少Label列")
                        skipped_files.append(file)
                        continue
                    # 保证标签为整数类型
                    df['Label'] = df['Label'].astype(int)

                    # 添加到对应的数据集列表
                    target_dfs.append(df)
                    successful_files.append(filepath)
                    print(f"成功处理: {file}, 形状: {df.shape}")

                except Exception as e:
                    print(f"处理文件 {file} 失败：{str(e)}")
                    import traceback
                    traceback.print_exc()
                    skipped_files.append(file)

    # 处理和合并数据
    print(f"\n成功处理的文件数量: {len(successful_files)}")
    print(f"跳过的文件数量: {len(skipped_files)}")
    if skipped_files:
        print("跳过的文件列表:")
        for file in skipped_files:
            print(f" - {file}")

    # 处理列名重复问题的函数
    def handle_duplicate_columns(dfs_list):
        for i, df in enumerate(dfs_list):
            # 检查是否有重复列名
            if len(df.columns) != len(set(df.columns)):
                print(f"警告: 数据框 {i} 有重复列名")
                # 找出重复的列名
                duplicated_cols = [col for col in df.columns if list(df.columns).count(col) > 1]
                print(f"重复列名: {set(duplicated_cols)}")

                # 重命名重复列
                for col in duplicated_cols:
                    # 找出所有重复列的索引
                    indices = [i for i, x in enumerate(df.columns) if x == col]
                    # 保留第一个，重命名其他的
                    for j, idx in enumerate(indices[1:], 1):
                        new_col = f"{col}_{j}"
                        print(f"将重复列 {col} 重命名为 {new_col}")
                        df.columns.values[idx] = new_col

    # 合并数据集的函数
    def merge_dataframes(dfs_list, dataset_name):
        if not dfs_list:
            print(f"\n警告：{dataset_name}数据集为空！")
            return pd.DataFrame()

        try:
            # 处理重复列名
            handle_duplicate_columns(dfs_list)

            # 尝试合并数据集
            merged_df = pd.concat(dfs_list, ignore_index=True)
            print(f"合并后{dataset_name}数据集大小: {merged_df.shape}")

            # 检查是否有Label列
            if 'Label' in merged_df.columns:
                print(f"{dataset_name}标签分布: 0: {(merged_df['Label']==0).sum()}, 1: {(merged_df['Label']==1).sum()}")
            else:
                print(f"警告: 合并后的{dataset_name}数据集中没有'Label'列")
                # 检查是否有类似标签的列
                label_cols = [col for col in merged_df.columns if 'label' in col.lower() or '标签' in col.lower()]
                if label_cols:
                    print(f"找到可能的标签列: {label_cols}")
                    # 使用第一个作为标签列
                    merged_df['Label'] = merged_df[label_cols[0]]
                    print(f"将 {label_cols[0]} 设置为标签列")
                    print(f"{dataset_name}标签分布: 0: {(merged_df['Label']==0).sum()}, 1: {(merged_df['Label']==1).sum()}")

            return merged_df

        except Exception as e:
            print(f"合并{dataset_name}数据集时出错: {str(e)}")
            print(f"尝试替代方法合并{dataset_name}数据集...")

            # 确保所有数据框都有相同的列
            all_columns = set()
            for df in dfs_list:
                all_columns.update(df.columns)

            # 将缺失的列添加到每个数据框
            for i, df in enumerate(dfs_list):
                missing_cols = all_columns - set(df.columns)
                for col in missing_cols:
                    dfs_list[i][col] = 0  # 用零填充缺失的列

            # 再次尝试合并
            merged_df = pd.concat(dfs_list, ignore_index=True)
            print(f"合并后{dataset_name}数据集大小: {merged_df.shape}")

            if 'Label' in merged_df.columns:
                print(f"{dataset_name}标签分布: 0: {(merged_df['Label']==0).sum()}, 1: {(merged_df['Label']==1).sum()}")

            return merged_df

    # 根据文件夹结构返回不同的结果
    if has_separate_folders:
        train_df = merge_dataframes(train_dfs, "训练集")
        test_df = merge_dataframes(test_dfs, "测试集")

        # 检查是否有数据
        if len(train_df) == 0 and len(test_df) == 0:
            print("\n警告：训练集和测试集都为空！")
            return pd.DataFrame(), None
        elif len(train_df) == 0:
            print("\n警告：训练集为空，只返回测试集！")
            return test_df, None
        elif len(test_df) == 0:
            print("\n警告：测试集为空，只返回训练集！")
            return train_df, None

        return train_df, test_df
    else:
        combined_df = merge_dataframes(all_dfs, "合并")

        if len(combined_df) == 0:
            print("\n警告：未加载到任何有效数据！")
            return pd.DataFrame(), None

        return combined_df, None

# 添加辅助函数处理 .xls 文件
def handle_xls_file(filepath):
    """手动处理 .xls 文件"""
    try:
        # 尝试使用 xlrd 1.2.0 版本读取
        import xlrd
        print(f"当前使用的xlrd版本: {xlrd.__version__}")

        # 如果版本太高，需要降级处理
        if xlrd.__version__ >= "2.0.0":
            print("警告: xlrd 2.0及以上版本不支持.xls，尝试替代方案")

            # 替代方案1: 尝试使用第三方模块
            try:
                import pyexcel_xls
                data = pyexcel_xls.get_data(filepath)
                sheet_name = list(data.keys())[0]
                sheet_data = data[sheet_name]

                # 从行数据中提取列名和数据
                headers = sheet_data[0]
                rows = sheet_data[1:]

                return pd.DataFrame(rows, columns=headers)
            except ImportError:
                print("pyexcel_xls 未安装，尝试其他方法...")
            except Exception as e:
                print(f"pyexcel_xls读取失败: {str(e)}")

            # 替代方案2: 尝试将xls文件转换为xlsx (需要win32com)
            try:
                import win32com.client
                import tempfile
                import os

                print("尝试使用Excel应用程序转换文件格式...")
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False

                # 创建临时文件名
                temp_dir = tempfile.gettempdir()
                temp_xlsx = os.path.join(temp_dir, "temp_converted.xlsx")

                try:
                    wb = excel.Workbooks.Open(os.path.abspath(filepath))
                    wb.SaveAs(temp_xlsx, 51)  # 51是xlsx格式的代码
                    wb.Close()
                    excel.Quit()

                    # 读取转换后的文件
                    result_df = pd.read_excel(temp_xlsx, engine='openpyxl')

                    # 删除临时文件
                    try:
                        os.remove(temp_xlsx)
                    except:
                        pass

                    return result_df
                except Exception as conv_err:
                    if 'excel' in locals():
                        excel.Quit()
                    print(f"转换过程中出错: {str(conv_err)}")
                    raise
            except ImportError:
                print("win32com 未安装，尝试其他方法...")
            except Exception as e:
                print(f"使用Excel转换失败: {str(e)}")

            # 如果上述方法都失败，尝试退回到读取CSV (如果存在对应的CSV文件)
            csv_path = filepath.replace('.xls', '.csv')
            if os.path.exists(csv_path):
                print(f"尝试读取同名CSV文件: {csv_path}")
                return pd.read_csv(csv_path)

            raise Exception("所有读取.xls文件的方法均失败")

        else:
            # xlrd 1.x 版本可以正常读取 .xls
            wb = xlrd.open_workbook(filepath)
            sheet = wb.sheet_by_index(0)

            # 从xlrd工作表构建DataFrame
            data = []
            for i in range(sheet.nrows):
                data.append(sheet.row_values(i))

            # 提取列名和数据行
            headers = data[0]
            rows = data[1:]

            return pd.DataFrame(rows, columns=headers)
    except Exception as e:
        print(f"手动处理.xls文件失败: {str(e)}")
        raise

#%% 模型结构（ BiLSTM with Attention 和 特征权重机制）
class DrillingModel(nn.Module):
    def __init__(self, input_size=4, hidden_size=64):  # 默认输入维度为4
        super().__init__()

        # 特征权重机制 - 根据物理机制设置特征权重
        # 创建特征权重参数，初始化为物理机制中的重要性顺序
        self.feature_weights = nn.Parameter(torch.ones(input_size), requires_grad=True)

        # 初始化特征权重 - 根据物理机制的重要性
        if input_size >= 8:  # 确保有足够的特征（修改为8个特征）
            # 基本特征权重 - 按照物理机制的重要性顺序
            # FlowOutPercent(0), SPP(1), TG(2), Sum(3) 的权重
            # 出口流量 > 总池体积 > 立管压力 > 总烃
            with torch.no_grad():
                # 为FINAL_FEATURES中的特征设置初始权重
                # 'FlowOutPercent', 'SPP', 'TG', 'Sum', 'FlowOut_Sum_Joint', 'FlowOut_SPP_Inverse',
                # 'TG_FlowOut_Joint', 'FlowOut_Rate_Change', 'SPP_Decrease_Sustained', 'Multi_Param_Anomaly'
                weights = torch.ones(input_size)

                # 基本特征权重 - 基于新的FINAL_FEATURES列表更新索引
                feature_indices = {
                    'FlowOutPercent': 0,      # 出口流量百分比 - 最重要
                    'SPP': 1,                 # 立管压力
                    'TG': 2,                  # 总烃
                    'Sum': 3,                 # 总池体积 - 第二重要

                    # 衍生特征权重 - 更新索引以匹配新的FINAL_FEATURES列表
                    'FlowOut_Sum_Joint': 4,       # 出口流量与总池体积协同变化 - 重要
                    'FlowOut_Rate_Change': 5,     # 出口流量变化率 - 重要
                    'Multi_Param_Anomaly': 6,     # 多参数异常协同指数 - 重要
                    'TG_FlowOut_Joint': 7,        # 总烃与出口流量协同变化

                    # 以下特征已从FINAL_FEATURES中移除，但保留索引映射以便将来可能重新启用
                    'FlowOut_SPP_Inverse': -1,    # 出口流量上升与立管压力下降的反相关 (已移除)
                    'SPP_Decrease_Sustained': -1  # 立管压力持续下降 (已移除)
                }

                # 设置权重值 - 基于错误分析结果调整，体现真正的重要性差异
                weights[feature_indices['FlowOutPercent']] = 2.0  # 出口流量百分比最重要，显著增强权重
                weights[feature_indices['TG']] = 1.6              # 总烃，大幅增强权重
                weights[feature_indices['Sum']] = 0.8             # 总池体积，降低权重
                weights[feature_indices['SPP']] = 0.6             # 立管压力，降低权重

                # 衍生特征权重 - 基于错误分析结果调整
                weights[feature_indices['FlowOut_Sum_Joint']] = 1.5       # 出口流量与总池体积协同变化，增强权重
                weights[feature_indices['FlowOut_Rate_Change']] = 1.6     # 出口流量变化率，增强权重
                weights[feature_indices['Multi_Param_Anomaly']] = 0.7     # 多参数异常协同指数，降低权重
                weights[feature_indices['TG_FlowOut_Joint']] = 1.4        # 总烃与出口流量协同变化，增强权重

                # 以下特征已从FINAL_FEATURES中移除，不再设置权重
                # weights[feature_indices['FlowOut_SPP_Inverse']] = 1.5     # 出口流量上升与立管压力下降的反相关 (已移除)
                # weights[feature_indices['SPP_Decrease_Sustained']] = 0.5  # 立管压力持续下降 (已移除)

                self.feature_weights.data = weights
                print("已初始化特征权重，根据物理机制的重要性顺序")

                # 打印初始化的权重值
                print("初始化的特征权重值:")
                for i, feature_name in enumerate(['FlowOutPercent', 'SPP', 'TG', 'Sum',
                                                'FlowOut_Sum_Joint', 'FlowOut_Rate_Change',
                                                'Multi_Param_Anomaly', 'TG_FlowOut_Joint']):
                    if i < len(weights):
                        print(f"- {feature_name}: {weights[i].item():.2f}")
                print()

        # BiLSTM层
        self.lstm = nn.LSTM(input_size, hidden_size,
                          bidirectional=True,
                          num_layers=2,
                          batch_first=True,
                          dropout=0.3)  # 添加dropout以提高泛化能力

        # 注意力机制
        self.attention = nn.Sequential(
            nn.Linear(hidden_size*2, 32),
            nn.Tanh(),
            nn.Linear(32, 1),
            nn.Softmax(dim=1)
        )

        # 分类器
        self.classifier = nn.Linear(hidden_size*2, 2)

    def forward(self, x):
        # 应用特征权重 - 增强重要特征的影响
        # 获取输入张量的形状，但不需要显式使用这些变量
        # 直接使用self.feature_weights的形状进行广播
        weighted_x = x * self.feature_weights.view(1, 1, -1)

        # LSTM处理
        lstm_out, _ = self.lstm(weighted_x)

        # 注意力机制
        attn_weights = self.attention(lstm_out)
        context = torch.sum(attn_weights * lstm_out, dim=1)

        # 分类
        logits = self.classifier(context)

        return logits

# 将数据转换为时间序列格式
def create_sequences(df_features, df_labels=None, sequence_length=30):
    """
    将特征数据和标签转换为时间序列格式。
    :param df_features: 输入的 DataFrame，只包含特征列。
    :param df_labels: 输入的 Series 或 array，包含标签。如果为 None，则生成全 0 标签。
    :param sequence_length: 序列长度。
    :return: 特征序列 X 和对应的标签 y (NumPy 数组)，以及实际使用的特征列列表。
    """
    # 确保特征列是列表类型
    feature_cols = list(df_features.columns)
    data = df_features.values # 直接使用传入的特征数据

    # 如果标签为 None，生成全 0 标签
    if df_labels is None:
        labels = np.zeros(len(df_features))
    else:
        # 如果是 Series，转换为 NumPy 数组
        if isinstance(df_labels, pd.Series):
            labels = df_labels.values
        else:
            labels = df_labels

    X = []
    sequence_labels = [] # 存储每个序列对应的真实标签 (序列最后一个点)

    # 确保数据点足够生成序列
    if len(data) < sequence_length:
        print(f"警告: 数据点不足 ({len(data)}) 以构建长度为 {sequence_length} 的序列!")
        return np.array([]), np.array([]), feature_cols

    for i in range(len(data) - sequence_length + 1): # 循环到最后一个完整的序列
        X.append(data[i : i + sequence_length])
        sequence_labels.append(labels[i + sequence_length - 1])  # 标签对应序列的最后一个点

    # 返回 NumPy 数组，并明确返回特征列列表
    return np.array(X).astype(np.float32), np.array(sequence_labels).astype(np.int64), feature_cols

# 训练循环部分需要修改
def train_model(model, train_loader, val_loader, test_loader, criterion, optimizer,
                device, epochs=100, patience=5, clip_grad_norm=1.0, callback=None):
    """
    训练模型并实现早停，使用验证集进行早停判断，测试集仅用于最终评估

    参数:
        model: 模型
        train_loader: 训练集数据加载器
        val_loader: 验证集数据加载器，用于早停判断
        test_loader: 测试集数据加载器，仅用于最终评估
        criterion: 损失函数
        optimizer: 优化器
        device: 计算设备
        epochs: 最大训练轮数
        patience: 早停耐心值
        clip_grad_norm: 梯度裁剪阈值
        callback: 回调函数，用于更新UI

    返回:
        history: 训练历史记录
        best_val_f1: 最佳验证集F1分数
        best_epoch: 最佳模型的轮数
        test_metrics: 测试集上的评估指标
    """
    best_val_f1 = 0  # 使用F1分数而非准确率进行早停判断
    best_epoch = 0
    no_improve_epochs = 0
    best_model_state = None

    print("开始训练...")
    print(f"设备: {device}, 训练样本: {len(train_loader.dataset)}, "
          f"验证样本: {len(val_loader.dataset)}, 测试样本: {len(test_loader.dataset)}")

    history = {'train_loss': [], 'train_acc': [], 'val_acc': [], 'val_f1': []}

    for epoch in range(epochs):
        model.train()
        total_loss = 0
        correct = 0
        total = 0

        for inputs, labels in train_loader:
            # 确保数据类型正确
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()

            optimizer.zero_grad()

            try:
                # 打印用于调试
                if epoch == 0 and total == 0:
                    print(f"输入形状: {inputs.shape}, 类型: {inputs.dtype}")
                    print(f"标签分布: {torch.bincount(labels)}")

                outputs = model(inputs).float()
                loss = criterion(outputs, labels)

                # 检查损失值是否为NaN
                if torch.isnan(loss).any():
                    print(f"警告: 发现NaN损失! 跳过此批次")
                    continue

                loss.backward()

                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)

                optimizer.step()

                total_loss += loss.item()

                # 计算训练准确度
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

            except RuntimeError as e:
                print(f"训练批次错误: {str(e)}")
                continue

        # 计算平均损失和准确率
        if total > 0:
            avg_loss = total_loss / len(train_loader)
            train_acc = correct / total
            history['train_loss'].append(avg_loss)
            history['train_acc'].append(train_acc)
        else:
            avg_loss = float('nan')
            train_acc = 0

        # 在验证集上评估 - 用于早停判断
        val_acc, val_f1 = evaluate_model(model, val_loader, device)
        history['val_acc'].append(val_acc)
        history['val_f1'].append(val_f1)

        # 打印进度
        print(f"Epoch {epoch+1:03d} | Loss: {avg_loss:.4f} | Train Acc: {train_acc:.4f} | "
              f"Val Acc: {val_acc:.4f} | Val F1: {val_f1:.4f}")

        # 如果提供了回调函数，调用它来更新UI
        if callback is not None:
            progress = int((epoch + 1) / epochs * 100)
            callback(epoch, avg_loss, train_acc, val_acc, progress)

        # 保存最佳模型 - 基于验证集F1分数
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_epoch = epoch
            best_model_state = model.state_dict().copy()  # 保存模型状态
            torch.save(model.state_dict(), "best_drilling_model.pth")
            print(f"Epoch {epoch+1:03d} | 保存新的最佳模型 (Val F1: {val_f1:.4f})")
            no_improve_epochs = 0
        else:
            no_improve_epochs += 1

        # 早停 - 基于验证集F1分数
        if no_improve_epochs >= patience:
            print(f"早停! {patience} 轮验证集F1分数没有改善")
            break

    print(f"\n训练完成! 最佳模型在第 {best_epoch+1} 轮, 验证集F1分数: {best_val_f1:.4f}")

    # 加载最佳模型进行测试集评估
    if best_model_state is not None:
        model.load_state_dict(best_model_state)

    # 在测试集上进行最终评估
    test_acc, test_f1 = evaluate_model(model, test_loader, device)
    print(f"测试集评估结果 - 准确率: {test_acc:.4f}, F1分数: {test_f1:.4f}")

    test_metrics = {'accuracy': test_acc, 'f1': test_f1}

    return history, best_val_f1, best_epoch, test_metrics

def evaluate_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    correct = 0
    total = 0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs = inputs.to(device).float()
            labels = labels.to(device).long()

            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)

            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # 计算F1分数
    from sklearn.metrics import f1_score
    f1 = f1_score(all_labels, all_preds, average='binary')

    return correct / total, f1

def analyze_feature_importance(model, features, device=None):
    """使用Captum分析特征重要性并绘制图表，结合模型的特征权重

    参数:
        model: 训练好的模型
        features: 特征名称列表
        device: 计算设备，如果为None则自动检测

    返回:
        importance_dict: 特征重要性字典，键为特征名，值为重要性分数
    """
    if not CAPTUM_AVAILABLE:
        print("警告: Captum库不可用，无法进行特征重要性分析")
        return None

    if model is None:
        print("警告: 模型未加载，无法进行特征重要性分析")
        return None

    try:
        # 确定计算设备
        if device is None:
            device = next(model.parameters()).device

        # 创建一个示例输入数据
        seq_length = 30  # 默认序列长度
        input_size = len(features)
        inputs = torch.randn(10, seq_length, input_size).to(device)  # 创建10个样本以增强稳定性

        # 创建集成梯度对象
        ig = IntegratedGradients(model)

        # 计算特征重要性
        attributions, _ = ig.attribute(inputs, target=1, return_convergence_delta=True)

        # 将归因结果求平均，得到每个特征的重要性分数
        feature_importance = attributions.abs().mean(dim=(0, 1)).detach().cpu().numpy()

        # 获取模型中的特征权重
        if hasattr(model, 'feature_weights'):
            # 结合模型的特征权重调整特征重要性
            model_weights = model.feature_weights.detach().cpu().numpy()
            print("模型特征权重:")
            for i, (name, weight) in enumerate(zip(features, model_weights)):
                print(f"- {name}: {weight:.4f}")

            # 将特征重要性与模型权重结合
            # 增加模型权重的影响比例，确保物理机制的重要性得到更好体现
            combined_importance = (feature_importance * 0.3) + (model_weights * 0.7)
            print("\n结合模型权重后的特征重要性:")
            for i, (name, imp) in enumerate(zip(features, combined_importance)):
                print(f"- {name}: {imp:.6f}")

            # 使用结合后的重要性
            feature_importance = combined_importance

        # 创建特征重要性排序图
        plt.figure(figsize=(10, 6))

        # 排序特征重要性
        indices = np.argsort(feature_importance)
        sorted_importances = feature_importance[indices]
        sorted_names = [features[i] for i in indices]

        # 绘制水平条形图
        y_pos = np.arange(len(sorted_names))
        plt.barh(y_pos, sorted_importances, align='center')
        plt.yticks(y_pos, sorted_names)
        plt.gca().invert_yaxis()  # 最重要的特征在顶部
        plt.xlabel('特征重要性分数')
        plt.title('特征重要性排序')
        plt.tight_layout()

        # 保存图表
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图表以释放资源
        print(f"特征重要性图表已保存为 'feature_importance.png'")

        # 返回特征重要性排序结果
        importance_dict = dict(zip(features, feature_importance))

        # 打印特征重要性排序
        print("\n最终特征重要性排序:")
        for name, importance in sorted(importance_dict.items(), key=lambda x: x[1], reverse=True):
            print(f"- {name}: {importance:.6f}")

        return importance_dict

    except Exception as e:
        import traceback
        error_msg = f"特征重要性分析失败: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return None

def generate_dataset_description():
    """
    生成数据集描述统计信息，用于论文撰写

    此函数加载数据，处理特征，生成序列，划分数据集，并应用SMOTE增强，
    然后输出详细的数据集统计信息，包括原始数据点数、序列样本数、
    各数据集中溢流样本的数量和比例，以及SMOTE增强后的标签分布。

    返回:
        str: 格式化的数据集描述文本，可直接用于论文
    """
    # 加载数据
    print("数据加载中...")
    train_df, test_df = load_data()

    # 处理数据
    if train_df is None or len(train_df) == 0:
        print("错误：未加载到有效数据")
        return "数据加载失败，无法生成描述"

    # 统计原始数据点数
    total_data_points = len(train_df) + (len(test_df) if test_df is not None else 0)

    # 使用训练集数据进行特征处理
    df = train_df

    # 处理原始特征的异常值
    raw_feature_cols_for_scaling = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
    raw_feature_cols_for_scaling = [col for col in raw_feature_cols_for_scaling if col in df.columns]

    # 移除异常值（仅对原始特征）
    for col in raw_feature_cols_for_scaling:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower = Q1 - 1.5 * IQR
        upper = Q3 + 1.5 * IQR
        df[col] = np.clip(df[col], lower, upper)

    # 检查是否有NaN值，并填充
    if df.isna().any().any():
        df = df.fillna(0)

    # 确保 FINAL_FEATURES 中的所有列都存在于 df 中
    for col in FINAL_FEATURES:
        if col not in df.columns:
            df[col] = 0

    # 提取最终的特征数据和标签
    df_features = df[FINAL_FEATURES]
    df_labels = df['Label']

    # 处理NaN或Infinite值
    if df_features.isnull().values.any() or np.isinf(df_features.values).any():
        df_features = df_features.replace([np.inf, -np.inf], np.nan).fillna(0)

    # 数据标准化
    scaler = StandardScaler()
    df_features_scaled = pd.DataFrame(
        scaler.fit_transform(df_features),
        columns=FINAL_FEATURES
    )

    # 生成序列数据
    sequence_length = 30
    X, y, used_features = create_sequences(df_features_scaled, df_labels, sequence_length=sequence_length)

    # 实现三分法数据划分 (训练集：验证集：测试集 = 6:2:2)
    test_size = 0.2
    val_size = 0.2

    # 首先划分出测试集
    test_split_idx = int(len(X) * (1 - test_size))
    X_temp, X_test = X[:test_split_idx], X[test_split_idx:]
    y_temp, y_test = y[:test_split_idx], y[test_split_idx:]

    # 然后从剩余数据中划分出验证集
    val_ratio_in_temp = val_size / (1 - test_size)
    val_split_idx = int(len(X_temp) * (1 - val_ratio_in_temp))

    # 按顺序划分，保持时间连续性
    X_train, X_val = X_temp[:val_split_idx], X_temp[val_split_idx:]
    y_train, y_val = y_temp[:val_split_idx], y_temp[val_split_idx:]

    # 计算各数据集的原始数据点数（考虑序列长度）
    train_data_points = len(X_train) + sequence_length - 1
    val_data_points = len(X_val) + sequence_length - 1
    test_data_points = len(X_test) + sequence_length - 1

    # 使用SMOTE进行数据增强
    n_samples, seq_len, n_features = X_train.shape
    X_train_reshaped = X_train.reshape(n_samples, seq_len * n_features)

    # 创建SMOTE实例并应用
    smote = SMOTE(random_state=42)
    X_train_resampled, y_train_resampled = smote.fit_resample(X_train_reshaped, y_train)

    # 将结果转回三维形状
    X_train_final = X_train_resampled.reshape(-1, seq_len, n_features)
    y_train_final = y_train_resampled

    # 计算SMOTE后的标签分布
    train_label_counts = np.bincount(y_train_final)

    # 生成描述文本
    description = f"""原始数据总计：{total_data_points}个数据点。
训练集：{train_data_points}个数据点，生成{len(X_train)}个序列样本。其中溢流样本{np.sum(y_train == 1)}个 (占总训练序列{np.sum(y_train == 1)/len(y_train):.2%})。
验证集：{val_data_points}个数据点，生成{len(X_val)}个序列样本。其中溢流样本{np.sum(y_val == 1)}个 (占总验证序列{np.sum(y_val == 1)/len(y_val):.2%})。
测试集：{test_data_points}个数据点，生成{len(X_test)}个序列样本。其中溢流样本{np.sum(y_test == 1)}个 (占总测试序列{np.sum(y_test == 1)/len(y_test):.2%})。
经过SMOTE对训练集进行过采样处理后，训练集中的溢流样本数量增加到{train_label_counts[1]}个，与正常样本数量达到平衡，标签分布为 0:{train_label_counts[0]}个, 1:{train_label_counts[1]}个。验证集和测试集保持原始分布不变。"""

    print("\n数据集描述统计信息：")
    print(description)

    # 保存描述到文件
    try:
        with open('dataset_description.txt', 'w', encoding='utf-8') as f:
            f.write(description)
        print("数据集描述已保存到 'dataset_description.txt'")
    except Exception as e:
        print(f"保存数据集描述失败: {str(e)}")

    return description

#%% 主程序流程
if __name__ == "__main__":
    # 检查是否有命令行参数
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--generate-description":
        # 生成数据集描述统计信息
        print("生成数据集描述统计信息...")
        generate_dataset_description()
        sys.exit(0)

    # 加载并预处理数据
    print("数据加载中...")
    train_df, test_df = load_data()

    # 使用训练集数据进行特征处理和模型训练
    df = train_df

    if df is None or len(df) == 0:
        print("错误：未加载到有效数据，程序终止")
        sys.exit(1)

    # 注意：我们将在筛选出 FINAL_FEATURES 后再进行标准化
    # 这里先处理原始特征的异常值
    raw_feature_cols_for_scaling = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
    # 过滤掉数据中不存在的列
    raw_feature_cols_for_scaling = [col for col in raw_feature_cols_for_scaling if col in df.columns]
    print(f"处理异常值的原始特征列: {raw_feature_cols_for_scaling}")

    # 移除异常值（仅对原始特征）
    for col in raw_feature_cols_for_scaling:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower = Q1 - 1.5 * IQR
        upper = Q3 + 1.5 * IQR
        df[col] = np.clip(df[col], lower, upper)

    # 检查是否有NaN值，并填充 (在计算完所有特征后进行填充)
    if df.isna().any().any():
        print("警告: 发现NaN值，使用0填充")
        df = df.fillna(0)

    # ** 筛选出需要用于标准化、序列化和训练的最终特征列 **
    print(f"原始数据列数 (包含所有加载和计算的特征): {len(df.columns)}")
    print(f"希望使用的最终特征列数: {len(FINAL_FEATURES)}")

    # 确保 FINAL_FEATURES 中的所有列都存在于 df 中，如果不存在则添加并用0填充
    for col in FINAL_FEATURES:
        if col not in df.columns:
            df[col] = 0
            print(f"警告: 最终特征 '{col}' 在数据中不存在，已添加并用0填充。")

    # 提取最终的特征数据和标签
    # 只保留 FINAL_FEATURES 列表中的列，并确保顺序一致
    df_features = df[FINAL_FEATURES]
    df_labels = df['Label'] # 假设 'Label' 列肯定存在

    # 检查是否有 NaN 或 Infinite 值，标准化器对这些值敏感
    if df_features.isnull().values.any() or np.isinf(df_features.values).any():
        print("警告: 最终特征数据中包含 NaN 或 Infinite 值，进行填充。")
        df_features = df_features.replace([np.inf, -np.inf], np.nan).fillna(0) # 将Inf替换为NaN再填充0

    # ** 数据标准化 **
    # 在最终筛选出的特征列上拟合和转换
    scaler = StandardScaler()
    df_features_scaled = pd.DataFrame(
        scaler.fit_transform(df_features),
        columns=FINAL_FEATURES
    ) # 转换回DataFrame以保留列名

    # --- 保存 scaler 的代码 ---
    import joblib
    scaler_filename = "scaler.pkl"
    joblib.dump(scaler, scaler_filename)
    print(f"标准化器已保存到 {scaler_filename}")
    # ------------------------------

    # --- 添加保存 used_features 的代码 ---
    import json
    features_filename = "used_features.json"
    with open(features_filename, 'w') as f:
        json.dump(FINAL_FEATURES, f) # 保存明确定义的最终特征列表
    print(f"使用的特征列表已保存到 {features_filename}")
    # ------------------------------------

    # ** 生成序列数据 **
    sequence_length = 30 # 定义序列长度
    X, y, used_features = create_sequences(df_features_scaled, df_labels, sequence_length=sequence_length) # 传递特征DF和标签DF
    input_size = len(used_features)  # 动态设置输入大小
    print(f"生成的序列特征形状: {X.shape}, 标签形状: {y.shape}, 特征数量: {input_size}")
    print(f"实际使用的特征列 (应该与FINAL_FEATURES一致): {used_features}")

    # 特征列表已经保存，不需要再次保存

    # 检查类别分布
    unique_labels, counts = np.unique(y, return_counts=True)
    for label, count in zip(unique_labels, counts):
        print(f"类别 {label} 样本数: {count}，占比: {count/len(y):.2%}")

    # 执行数据增强
    print("执行数据增强以扩充数据集...")
    # 找出少数类和多数类样本索引
    minority_indices = np.where(y == 1)[0] if np.sum(y == 1) < np.sum(y == 0) else np.where(y == 0)[0]
    majority_indices = np.where(y == 0)[0] if np.sum(y == 1) < np.sum(y == 0) else np.where(y == 1)[0]

    # 确定少数类和多数类标签
    minority_label = 1 if np.sum(y == 1) < np.sum(y == 0) else 0
    majority_label = 0 if minority_label == 1 else 1

    print(f"原始数据形状: {X.shape}, 标签分布: {np.bincount(y)}")
    print(f"多数类标签为 {majority_label}, 样本数: {len(majority_indices)}")
    print(f"少数类标签为 {minority_label}, 样本数: {len(minority_indices)}")

    # 使用SMOTE进行数据增强
    # 检查少数类样本数量是否足够进行SMOTE
    min_samples_needed = 6  # SMOTE默认k_neighbors=5，需要至少k+1个样本
    if len(minority_indices) >= min_samples_needed:
        try:
            # 将三维序列数据展平为二维进行SMOTE
            n_samples, seq_len, n_features = X.shape
            X_reshaped = X.reshape(n_samples, seq_len * n_features)

            # 创建SMOTE实例并应用
            smote = SMOTE(random_state=42)
            X_resampled, y_resampled = smote.fit_resample(X_reshaped, y)

            # 将结果转回三维形状
            X_final = X_resampled.reshape(-1, seq_len, n_features)
            y_final = y_resampled

            # 打印增强前后的对比
            original_counts = np.bincount(y)
            augmented_counts = np.bincount(y_final)

            # 计算原始和增强后的样本总数
            original_total = len(y)
            augmented_total = len(y_final)

            print(f"SMOTE后数据形状: {X_final.shape}, 标签分布: {augmented_counts}")
            print(f"原始数据: {X.shape}, 增强后: {X_final.shape}")
            print(f"增强前标签分布: 0: {original_counts[0]} ({original_counts[0]/original_total:.2%}), 1: {original_counts[1]} ({original_counts[1]/original_total:.2%})")
            print(f"增强后标签分布: 0: {augmented_counts[0]} ({augmented_counts[0]/augmented_total:.2%}), 1: {augmented_counts[1]} ({augmented_counts[1]/augmented_total:.2%})")

            # 更新数据集
            X, y = X_final, y_final
        except Exception as e:
            print(f"SMOTE增强失败: {str(e)}")
    else:
        print(f"少数类样本数量({len(minority_indices)})不足以进行SMOTE增强(需要至少{min_samples_needed}个)")

    # 实现三分法数据划分 (训练集：验证集：测试集 = 6:2:2)
    # 对于时间序列数据，严格按时间顺序划分，确保验证集和测试集的时间点晚于训练集
    test_size = 0.2
    val_size = 0.2

    # 首先划分出测试集
    test_split_idx = int(len(X) * (1 - test_size))
    X_temp, X_test = X[:test_split_idx], X[test_split_idx:]
    y_temp, y_test = y[:test_split_idx], y[test_split_idx:]

    # 然后从剩余数据中划分出验证集
    # 计算验证集在剩余数据中的比例
    val_ratio_in_temp = val_size / (1 - test_size)
    val_split_idx = int(len(X_temp) * (1 - val_ratio_in_temp))

    # 按顺序划分，保持时间连续性
    X_train, X_val = X_temp[:val_split_idx], X_temp[val_split_idx:]
    y_train, y_val = y_temp[:val_split_idx], y_temp[val_split_idx:]

    print("使用三分法时间顺序划分数据集 (训练集：验证集：测试集 = 6:2:2)")

    print(f"数据集总大小: {len(X)} 序列")
    print(f"训练集大小: {len(X_train)} 序列, 正样本: {np.sum(y_train == 1)}, 比例: {np.sum(y_train == 1)/len(y_train):.2%}")
    print(f"验证集大小: {len(X_val)} 序列, 正样本: {np.sum(y_val == 1)}, 比例: {np.sum(y_val == 1)/len(y_val):.2%}")
    print(f"测试集大小: {len(X_test)} 序列, 正样本: {np.sum(y_test == 1)}, 比例: {np.sum(y_test == 1)/len(y_test):.2%}")

    # 验证划分比例是否接近目标比例
    total = len(X)
    train_ratio = len(X_train) / total
    val_ratio = len(X_val) / total
    test_ratio = len(X_test) / total
    print(f"实际划分比例 - 训练集: {train_ratio:.2f}, 验证集: {val_ratio:.2f}, 测试集: {test_ratio:.2f}")

    # 数据集类
    class DrillingDataset(Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)  # 确保使用 FloatTensor (32位浮点数)
            self.y = torch.LongTensor(y)

        def __len__(self):
            return len(self.X)

        def __getitem__(self, idx):
            return self.X[idx], self.y[idx]

    # 创建训练、验证和测试数据集
    train_dataset = DrillingDataset(X_train, y_train)
    val_dataset = DrillingDataset(X_val, y_val)
    test_dataset = DrillingDataset(X_test, y_test)

    batch_size = 64
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    # 模型配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = DrillingModel(input_size=input_size).to(device)  # 使用动态特征数量

    # 处理类别不平衡 - 确保权重是 float32
    label_counts = np.bincount(y_train)
    # 反比例加权，少数类权重更大
    class_weight_factor = 1.0  # 降低为1.0，让权重计算更直接反映训练集的不平衡比例
    class_weights = torch.tensor([
        1.0,                                  # 类别 0 权重
        label_counts[0]/label_counts[1] * class_weight_factor # 类别 1 权重
    ], dtype=torch.float32).to(device)  # 明确指定 float32 类型

    # 打印权重类型以调试
    print(f"Class weights dtype: {class_weights.dtype}")
    print(f"Class weights: {class_weights}")

    criterion = nn.CrossEntropyLoss(weight=class_weights)

    # 确保模型参数为 float32 类型
    for param in model.parameters():
        param.data = param.data.float()

    # 优化器配置 (使用更小的学习率)
    optimizer = optim.AdamW(model.parameters(), lr=5e-4, weight_decay=1e-5)

    # 训练模型 (使用改进的训练函数，基于验证集进行早停)
    history, best_val_f1, best_epoch, test_metrics = train_model(
        model, train_loader, val_loader, test_loader, criterion, optimizer,
        device, epochs=100, patience=10, clip_grad_norm=1.0
    )

    # 可视化训练过程
    try:
        import matplotlib.pyplot as plt

        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 优先使用微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        # 创建两个子图，分别显示损失和准确率
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 绘制训练损失曲线 - 左侧子图
        epochs = list(range(len(history['train_loss'])))
        ax1.plot(epochs, history['train_loss'], 'b-', linewidth=2, label='训练集损失')
        ax1.set_title('Training Loss', fontsize=14)
        ax1.set_xlabel('Epoch', fontsize=12)
        ax1.set_ylabel('Loss', fontsize=12)
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.legend(fontsize=10)

        # 设置y轴范围，确保从0开始，便于观察
        y_max = max(history['train_loss']) * 1.1
        ax1.set_ylim(0, y_max)

        # 绘制准确率曲线 - 右侧子图
        ax2.plot(epochs, history['train_acc'], 'b-', linewidth=2, label='训练集准确率')
        ax2.plot(epochs, history['val_acc'], 'orange', linewidth=2, label='验证集准确率')

        # 设置标题和标签
        ax2.set_title('Accuracy', fontsize=14)
        ax2.set_xlabel('Epoch', fontsize=12)
        ax2.set_ylabel('Accuracy', fontsize=12)
        ax2.grid(True, linestyle='--', alpha=0.7)
        ax2.legend(fontsize=10)

        # 设置y轴范围，通常准确率从0.5开始更有意义
        y_min = min(min(history['train_acc']), min(history['val_acc']))
        y_min = max(0.5, y_min * 0.95)  # 下限不低于0.5
        y_max = max(max(history['train_acc']), max(history['val_acc'])) * 1.05
        y_max = min(1.0, y_max)  # 上限不超过1.0
        ax2.set_ylim(y_min, y_max)

        # 美化图表
        for ax in [ax1, ax2]:
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.tick_params(axis='both', which='major', labelsize=10)

        # 保存并显式关闭图形
        fig.tight_layout(pad=2.0)
        fig.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.close(fig)  # 明确关闭图形对象
        print(f"训练历史已保存为 'training_history.png'")

    except Exception as e:
        print(f"绘制图表失败: {e}")

    # 加载最佳模型进行最终评估
    model.load_state_dict(torch.load("best_drilling_model.pth"))

    # 在测试集上进行最终评估
    model.eval()
    true_labels = []
    pred_labels = []
    pred_probs = []  # 预测概率

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs = inputs.to(device).float()
            outputs = model(inputs)
            probs = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs.data, 1)

            true_labels.extend(labels.cpu().numpy())
            pred_labels.extend(predicted.cpu().numpy())
            pred_probs.extend(probs[:, 1].cpu().numpy())  # 保存正类概率

    # 计算混淆矩阵和分类报告
    from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc

    print("\n混淆矩阵:")
    cm = confusion_matrix(true_labels, pred_labels)
    print(cm)

    print("\n分类报告:")
    print(classification_report(true_labels, pred_labels))

    # 绘制ROC曲线
    try:
        fpr, tpr, _ = roc_curve(true_labels, pred_probs)
        roc_auc = auc(fpr, tpr)

        # 创建独立的figure对象
        fig = plt.figure(figsize=(8, 6))
        ax = fig.add_subplot(111)  # 显式创建坐标系

        # 使用axes对象绘图
        ax.plot(fpr, tpr, color='darkorange', lw=2,
                label=f'ROC curve (area = {roc_auc:.2f})')
        ax.plot([0, 1], [0, 1], color='navy', lw=1, linestyle='--')
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title('Receiver Operating Characteristic')
        ax.legend(loc="lower right")

        # 保存并显式关闭
        fig.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
        plt.close(fig)  # 明确释放资源
        print(f"ROC曲线已保存为 'roc_curve.png', AUC = {roc_auc:.4f}")

    except Exception as e:
        print(f"绘制ROC曲线失败: {str(e)}")

        # 尝试使用替代方法保存ROC数据
        print("尝试使用替代方法绘制ROC数据...")
        try:
            # 如果matplotlib有问题，直接保存ROC数据到CSV
            import pandas as pd
            from sklearn.preprocessing import StandardScaler
            roc_data = pd.DataFrame({
                'fpr': fpr,
                'tpr': tpr,
                'auc': [roc_auc] * len(fpr)
            })
            roc_data.to_csv('roc_data.csv', index=False)
            print(f"ROC数据已保存为CSV文件，AUC = {roc_auc:.4f}")
        except Exception as e2:
            print(f"保存ROC数据也失败: {str(e2)}")
            print(f"模型AUC值 = {roc_auc:.4f}")

    print(f"\n最佳验证集F1分数: {best_val_f1:.4f} (Epoch {best_epoch+1})")
    print(f"测试集评估结果 - 准确率: {test_metrics['accuracy']:.4f}, F1分数: {test_metrics['f1']:.4f}")
    print("训练完成，最佳模型已保存为 'best_drilling_model.pth'")

    # 分析特征重要性
    if CAPTUM_AVAILABLE:
        print("\n分析特征重要性...")
        feature_importance = analyze_feature_importance(model, FINAL_FEATURES, device)

        # 将特征重要性和特征权重保存到文件
        if feature_importance is not None:
            import json

            # 保存特征重要性
            with open('feature_importance.json', 'w') as f:
                json.dump({k: float(v) for k, v in feature_importance.items()}, f, indent=4)
            print(f"特征重要性已保存到 'feature_importance.json'")

            # 保存特征权重
            if hasattr(model, 'feature_weights'):
                feature_weights = {
                    FINAL_FEATURES[i]: float(model.feature_weights[i].detach().cpu().numpy())
                    for i in range(len(FINAL_FEATURES))
                }
                with open('feature_weights.json', 'w') as f:
                    json.dump(feature_weights, f, indent=4)
                print(f"特征权重已保存到 'feature_weights.json'")
    else:
        print("\n警告: Captum库不可用，跳过特征重要性分析")





