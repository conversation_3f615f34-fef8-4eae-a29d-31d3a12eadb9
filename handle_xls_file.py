import os
import pandas as pd

def handle_xls_file(filepath):
    """手动处理 .xls 文件"""
    try:
        import xlrd
        print(f"当前使用的xlrd版本: {xlrd.__version__}")
        if xlrd.__version__ >= "2.0.0":
            print("警告: xlrd 2.0及以上版本不支持.xls，尝试替代方案")
            try:
                import pyexcel_xls
                data = pyexcel_xls.get_data(filepath)
                sheet_name = list(data.keys())[0]
                sheet_data = data[sheet_name]
                headers = sheet_data[0]
                rows = sheet_data[1:]
                return pd.DataFrame(rows, columns=headers)
            except ImportError:
                print("pyexcel_xls 未安装，尝试其他方法...")
            except Exception as e:
                print(f"pyexcel_xls读取失败: {str(e)}")
            try:
                import win32com.client
                import tempfile
                print("尝试使用Excel应用程序转换文件格式...")
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False
                temp_dir = tempfile.gettempdir()
                temp_xlsx = os.path.join(temp_dir, "temp_converted.xlsx")
                try:
                    wb = excel.Workbooks.Open(os.path.abspath(filepath))
                    wb.SaveAs(temp_xlsx, 51)
                    wb.Close()
                    excel.Quit()
                    result_df = pd.read_excel(temp_xlsx, engine='openpyxl')
                    try:
                        os.remove(temp_xlsx)
                    except:
                        pass
                    return result_df
                except Exception as conv_err:
                    if 'excel' in locals():
                        excel.Quit()
                    print(f"转换过程中出错: {str(conv_err)}")
                    raise
            except ImportError:
                print("win32com 未安装，尝试其他方法...")
            except Exception as e:
                print(f"使用Excel转换失败: {str(e)}")
            csv_path = filepath.replace('.xls', '.csv')
            if os.path.exists(csv_path):
                print(f"尝试读取同名CSV文件: {csv_path}")
                return pd.read_csv(csv_path)
            raise Exception("所有读取.xls文件的方法均失败")
        else:
            wb = xlrd.open_workbook(filepath)
            sheet = wb.sheet_by_index(0)
            data = []
            for i in range(sheet.nrows):
                data.append(sheet.row_values(i))
            headers = data[0]
            rows = data[1:]
            return pd.DataFrame(rows, columns=headers)
    except Exception as e:
        print(f"手动处理.xls文件失败: {str(e)}")
        raise
