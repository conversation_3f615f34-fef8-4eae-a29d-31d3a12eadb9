import numpy as np
import pandas as pd
import torch
import pickle
import os
import re
from sklearn.preprocessing import StandardScaler

class PreprocessingPipeline:
    """
    统一的数据预处理管道，确保训练和预测时使用相同的预处理步骤
    """
    def __init__(self, preferred_features=None, sequence_length=30, spp_threshold=0, torque_threshold=0):
        """
        初始化预处理管道

        参数:
            preferred_features: 首选特征列表，默认为 ['SPP', 'Sum', 'FlowOutPercent', 'TG']
            sequence_length: 序列长度，默认为30
            spp_threshold: SPP特征的阈值，小于等于该值的数据点将被设为0
            torque_threshold: Torque特征的阈值，小于等于该值的数据点将被设为0
        """
        # 设置首选特征列表
        if preferred_features is None:
            self.preferred_features = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
        else:
            self.preferred_features = preferred_features

        self.sequence_length = sequence_length
        self.spp_threshold = spp_threshold
        self.torque_threshold = torque_threshold
        self.scalers = {}  # 每个特征的缩放器
        self.is_fitted = False
        self.used_features = []  # 实际使用的特征列表
        self.feature_medians = {}  # 用于填充缺失值的中位数

    def unify_columns(self, df):
        """
        统一列名，处理不同数据源的列名变化

        参数:
            df: 输入数据 DataFrame

        返回:
            列名统一后的 DataFrame
        """
        # 创建副本避免修改原始数据
        df_copy = df.copy()

        # 定义列名映射规则
        column_patterns = {
            'SPP': [r'(?i)spp', r'(?i)standpipe.*pressure'],
            'Sum': [r'(?i)sum', r'(?i)total.*volume'],
            'FlowOutPercent': [r'(?i)flow.*out.*percent', r'(?i)flow.*percent'],
            'TG': [r'(?i)tg', r'(?i)total.*gas'],
            'Label': [r'(?i)label', r'(?i)overflow', r'(?i)target']
        }

        # 应用列名映射
        for target_col, patterns in column_patterns.items():
            # 检查目标列是否已存在
            if target_col in df_copy.columns:
                continue

            # 尝试匹配模式
            for pattern in patterns:
                matching_cols = [col for col in df_copy.columns if re.search(pattern, col)]
                if matching_cols:
                    # 使用第一个匹配的列
                    df_copy[target_col] = df_copy[matching_cols[0]]
                    print(f"将列 '{matching_cols[0]}' 映射为 '{target_col}'")
                    break

        # 确保必要的列存在，如果不存在则添加全零列
        for col in self.preferred_features + ['Label']:
            if col not in df_copy.columns:
                print(f"警告: 添加缺失的列 '{col}' 并填充为0")
                df_copy[col] = 0

        return df_copy

    def preprocess_features(self, df):
        """
        预处理特征，应用阈值处理等

        参数:
            df: 输入数据 DataFrame

        返回:
            预处理后的 DataFrame
        """
        # 创建副本避免修改原始数据
        df_copy = df.copy()

        # 应用阈值处理
        if 'SPP' in df_copy.columns and self.spp_threshold > 0:
            df_copy.loc[df_copy['SPP'] <= self.spp_threshold, 'SPP'] = 0

        if 'Torque' in df_copy.columns and self.torque_threshold > 0:
            df_copy.loc[df_copy['Torque'] <= self.torque_threshold, 'Torque'] = 0

        # 处理缺失值
        if df_copy.isna().any().any():
            print("警告: 发现NaN值，使用中位数或零填充")
            for col in df_copy.columns:
                if df_copy[col].isna().any():
                    if self.is_fitted and col in self.feature_medians:
                        # 使用训练集的中位数填充
                        df_copy[col].fillna(self.feature_medians[col], inplace=True)
                    else:
                        # 如果没有拟合过或该列没有中位数，使用零填充
                        df_copy[col].fillna(0, inplace=True)

        return df_copy
    def fit(self, df_train):
        """
        在训练数据上拟合预处理管道

        参数:
            df_train: 训练数据DataFrame
        """
        # 统一列名
        df_train = self.unify_columns(df_train)

        # 预处理特征
        df_train = self.preprocess_features(df_train)

        # 计算并保存每个特征的中位数，用于填充缺失值
        for col in df_train.columns:
            # 检查列是否为数值类型
            if pd.api.types.is_numeric_dtype(df_train[col]):
                try:
                    self.feature_medians[col] = df_train[col].median()
                except Exception as e:
                    print(f"警告: 无法计算列 '{col}' 的中位数: {str(e)}")
                    self.feature_medians[col] = 0
            else:
                print(f"跳过非数值列 '{col}' 的中位数计算")
                self.feature_medians[col] = 0  # 非数值列使用零作为默认值

        # 确定要使用的特征列
        self.used_features = []
        for col in self.preferred_features:
            if col in df_train.columns and df_train[col].abs().sum() > 0:
                self.used_features.append(col)
            else:
                print(f"警告: 特征 {col} 不存在或全为零，将被忽略")

        if not self.used_features:
            raise ValueError("没有有效的特征列可用于训练")

        print(f"预处理管道使用的特征列: {self.used_features}")

        # 为每个特征创建并拟合缩放器
        for feature in self.used_features:
            # 移除异常值 (IQR方法)
            Q1 = df_train[feature].quantile(0.25)
            Q3 = df_train[feature].quantile(0.75)
            IQR = Q3 - Q1
            lower = Q1 - 1.5 * IQR
            upper = Q3 + 1.5 * IQR
            df_feature = np.clip(df_train[feature], lower, upper)

            # 创建并拟合缩放器
            scaler = StandardScaler()
            scaler.fit(df_feature.values.reshape(-1, 1))
            self.scalers[feature] = scaler

        self.is_fitted = True
        return self

    def transform(self, df):
        """
        使用拟合好的预处理管道转换数据

        参数:
            df: 要转换的DataFrame

        返回:
            转换后的DataFrame副本
        """
        if not self.is_fitted:
            raise ValueError("预处理管道尚未拟合，请先调用fit方法")

        # 统一列名
        df_transformed = self.unify_columns(df)

        # 预处理特征
        df_transformed = self.preprocess_features(df_transformed)

        # 确保所有使用的特征都存在
        for feature in self.used_features:
            if feature not in df_transformed.columns:
                print(f"警告: 特征 {feature} 在转换数据中不存在，添加全零列")
                df_transformed[feature] = 0
            elif df_transformed[feature].isna().any():
                # 如果有缺失值，使用训练集的中位数填充
                if feature in self.feature_medians:
                    df_transformed[feature].fillna(self.feature_medians[feature], inplace=True)
                else:
                    df_transformed[feature].fillna(0, inplace=True)

        # 应用缩放器转换每个特征
        for feature in self.used_features:
            # 检查列是否为数值类型
            if feature in self.scalers and pd.api.types.is_numeric_dtype(df_transformed[feature]):
                try:
                    df_transformed[feature] = self.scalers[feature].transform(
                        df_transformed[feature].values.reshape(-1, 1)
                    ).reshape(-1)
                except Exception as e:
                    print(f"警告: 无法转换列 '{feature}': {str(e)}")
            elif not pd.api.types.is_numeric_dtype(df_transformed[feature]):
                print(f"跳过非数值列 '{feature}' 的缩放器转换")

        return df_transformed

    def fit_transform(self, df_train):
        """
        拟合并转换训练数据

        参数:
            df_train: 训练数据DataFrame

        返回:
            转换后的DataFrame
        """
        self.fit(df_train)
        return self.transform(df_train)

    def create_sequences(self, df):
        """
        将转换后的数据转换为时间序列格式

        参数:
            df: 已转换的DataFrame

        返回:
            特征序列X，标签序列y，使用的特征列表
        """
        # 确保所有使用的特征都存在且为数值类型
        numeric_features = []
        for feature in self.used_features:
            if feature not in df.columns:
                print(f"警告: 特征 {feature} 在序列创建时不存在，添加全零列")
                df[feature] = 0
                numeric_features.append(feature)
            elif not pd.api.types.is_numeric_dtype(df[feature]):
                print(f"警告: 特征 {feature} 不是数值类型，将被跳过")
            else:
                numeric_features.append(feature)

        # 更新使用的特征列表，只保留数值特征
        if len(numeric_features) < len(self.used_features):
            print(f"警告: 原本使用 {len(self.used_features)} 个特征，现在只使用 {len(numeric_features)} 个数值特征")
            self.used_features = numeric_features

        # 确保有Label列
        if 'Label' not in df.columns:
            print("警告: 数据中没有Label列，添加默认标签0")
            df['Label'] = 0

        # 创建序列
        X, y = [], []

        # 确保有足够的数据创建序列
        if len(df) <= self.sequence_length:
            print(f"警告: 数据长度({len(df)})小于序列长度({self.sequence_length})，无法创建序列")
            return np.array([]), np.array([]), self.used_features

        # 确保有特征可用
        if not self.used_features:
            print("错误: 没有可用的数值特征来创建序列")
            return np.array([]), np.array([]), self.used_features

        try:
            data = df[self.used_features].values
            labels = df['Label'].values

            for i in range(len(data) - self.sequence_length):
                X.append(data[i:i + self.sequence_length])
                y.append(labels[i + self.sequence_length - 1])
        except Exception as e:
            print(f"创建序列时出错: {str(e)}")
            return np.array([]), np.array([]), self.used_features

        return np.array(X), np.array(y), self.used_features

    def save(self, filepath):
        """
        保存预处理管道到文件

        参数:
            filepath: 保存路径
        """
        with open(filepath, 'wb') as f:
            pickle.dump({
                'preferred_features': self.preferred_features,
                'used_features': self.used_features,
                'sequence_length': self.sequence_length,
                'spp_threshold': self.spp_threshold,
                'torque_threshold': self.torque_threshold,
                'scalers': self.scalers,
                'feature_medians': self.feature_medians,
                'is_fitted': self.is_fitted
            }, f)
        print(f"预处理管道已保存到: {filepath}")

    @classmethod
    def load(cls, filepath):
        """
        从文件加载预处理管道

        参数:
            filepath: 加载路径

        返回:
            加载的PreprocessingPipeline对象
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"预处理管道文件不存在: {filepath}")

        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        pipeline = cls(
            preferred_features=data['preferred_features'],
            sequence_length=data['sequence_length'],
            spp_threshold=data.get('spp_threshold', 0),
            torque_threshold=data.get('torque_threshold', 0)
        )
        pipeline.used_features = data['used_features']
        pipeline.scalers = data['scalers']
        pipeline.feature_medians = data.get('feature_medians', {})
        pipeline.is_fitted = data['is_fitted']

        return pipeline
