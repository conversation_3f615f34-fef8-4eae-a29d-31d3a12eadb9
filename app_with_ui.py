import os
import sys
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from PyQt5 import uic
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QMessageBox, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import logging
from logging.handlers import RotatingFileHandler
import psutil
import time
from datetime import datetime

# 从Bi_LSTM导入最终使用的特征列表
from Bi_LSTM import FINAL_FEATURES

# 配置日志系统
def setup_logging():
    """配置应用程序日志系统"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 配置根日志记录器
    logger = logging.getLogger('DrillingApp')
    logger.setLevel(logging.INFO)

    # 创建轮转文件处理器
    log_file = 'logs/drilling_app.log'
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler()

    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# 性能监控类
class PerformanceMonitor:
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = None
        self.logger = logging.getLogger('DrillingApp.Performance')

    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.initial_memory = self.process.memory_info().rss
        self.logger.info(f"开始性能监控 - 初始内存使用: {self.initial_memory/1024/1024:.2f}MB")

    def log_performance(self, operation):
        """记录性能指标"""
        current_memory = self.process.memory_info().rss
        cpu_percent = self.process.cpu_percent()
        duration = time.time() - self.start_time

        self.logger.info(
            f"{operation} - "
            f"耗时: {duration:.2f}秒, "
            f"内存使用: {current_memory/1024/1024:.2f}MB, "
            f"CPU使用率: {cpu_percent}%"
        )

# 初始化日志系统和性能监控
logger = setup_logging()
performance_monitor = PerformanceMonitor()

# 记录应用启动
logger.info("钻井数据分析应用程序启动")
performance_monitor.start_monitoring()
# 导入必要的模块
import traceback
from sklearn.preprocessing import StandardScaler  # 使用StandardScaler替代MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from imblearn.over_sampling import SMOTE  # 添加SMOTE导入

# 尝试导入Captum库用于模型可解释性
try:
    import captum
    from captum.attr import IntegratedGradients, LayerIntegratedGradients
    from captum.attr import visualization as viz
    CAPTUM_AVAILABLE = True
except ImportError:
    print("警告: 未找到Captum库，将不能使用特征重要性分析功能")
    CAPTUM_AVAILABLE = False

try:
    import seaborn as sns  # 用于绘制混淆矩阵
except ImportError:
    print("警告: 未找到seaborn库，将使用matplotlib绘制混淆矩阵")
    sns = None

# 从Bi_LSTM模块导入必要的类和函数
from Bi_LSTM import (
    DrillingModel,  # 神经网络模型类
    load_data,  # 加载数据函数
    create_sequences,  # 创建序列函数
    unify_columns,  # 统一列名函数
    preprocess_features,  # 预处理特征函数
    calculate_constraint_features,  # 计算约束特征函数
    train_model  # 导入Bi_LSTM的训练函数 - 确保这里导入train_model
)

# 环境检查函数
def check_environment():
    """检查运行环境，返回缺失的包和设备信息"""
    missing_packages = []

    # 检查必要的库
    required_packages = {
        'torch': 'PyTorch深度学习框架',
        'numpy': '数值计算库',
        'pandas': '数据分析库',
        'matplotlib': '绘图库',
        'PyQt5': 'GUI界面库',
        'sklearn': '机器学习库',
        'imblearn': 'SMOTE过采样库',
        'captum': 'PyTorch模型可解释性库'
    }

    for package, description in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(f"{package}: {description}")

    # 检查设备信息
    device_info = "CPU"
    try:
        import torch
        if torch.cuda.is_available():
            device_info = f"GPU: {torch.cuda.get_device_name(0)}"
    except:
        pass

    return missing_packages, device_info

# 应用初始化函数
def initialize_app():
    """初始化应用程序，返回配置信息"""
    # 创建必要的目录
    required_dirs = ['logs', 'models', 'results']
    for directory in required_dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"创建目录: {directory}")

    # 加载配置
    config = {
        'version': '1.0.0',
        'model_path': 'best_drilling_model.pth',
        'default_sequence_length': 30,
        'default_features': ['FlowOutPercent', 'SPP', 'TG', 'Sum',
                            'FlowOut_Sum_Joint', 'FlowOut_SPP_Inverse', 'TG_FlowOut_Joint',
                            'FlowOut_Rate_Change', 'SPP_Decrease_Sustained', 'Multi_Param_Anomaly']
    }

    logger.info(f"应用初始化完成，版本: {config['version']}")
    return config

# 资源清理函数
def cleanup_resources():
    """清理应用程序资源"""
    logger.info("正在清理应用程序资源...")

    # 记录性能统计
    performance_monitor.log_performance("应用程序结束")

    # 清理临时文件
    temp_files = ['temp_converted.xlsx', 'temp_data.npz']
    for file in temp_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                logger.info(f"已删除临时文件: {file}")
            except Exception as e:
                logger.error(f"删除临时文件失败: {file}, 错误: {str(e)}")

    # 关闭日志处理器
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)

    logger.info("应用程序资源清理完成")

# 添加prepare_model_file函数到本地
def prepare_model_file():
    """将Bi-LSTM.py复制为Bi_LSTM.py以便导入"""
    source = os.path.join(os.path.dirname(__file__), "Bi-LSTM.py")
    target = os.path.join(os.path.dirname(__file__), "Bi_LSTM.py")

    if os.path.exists(source) and not os.path.exists(target):
        import shutil
        shutil.copy2(source, target)
        print(f"已创建模型文件副本: {target}")

# 自定义图表类
class MatplotlibCanvas(FigureCanvas):
    """用于显示matplotlib图表的画布"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)

# 数据集类
class DrillingDataset(torch.utils.data.Dataset):
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

# 训练线程类
class TrainingThread(QThread):
    """训练模型的线程"""
    update_progress = pyqtSignal(int)
    update_metrics = pyqtSignal(dict)
    training_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, X_train, y_train, X_val, y_val, X_test, y_test, input_size, params):
        super().__init__()
        self.X_train = X_train
        self.y_train = y_train
        self.X_val = X_val
        self.y_val = y_val
        self.X_test = X_test
        self.y_test = y_test
        self.input_size = input_size
        self.params = params

    def run(self):
        try:
            import torch.nn as nn
            import torch.optim as optim
            from torch.utils.data import DataLoader
            import numpy as np  # 添加NumPy导入

            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

            # 创建数据加载器 - 训练集、验证集和测试集
            train_dataset = DrillingDataset(self.X_train, self.y_train)
            val_dataset = DrillingDataset(self.X_val, self.y_val)
            test_dataset = DrillingDataset(self.X_test, self.y_test)

            batch_size = self.params.get('batch_size', 64)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

            # 创建模型
            model = DrillingModel(input_size=self.input_size).to(device)

            # 处理类别不平衡
            label_counts = np.bincount(self.y_train)
            class_weight_factor = self.params.get('class_weight_factor', 1.0)  # 默认值改为1.0
            class_weights = torch.tensor([
                1.0,
                label_counts[0]/label_counts[1] * class_weight_factor
            ], dtype=torch.float32).to(device)

            criterion = nn.CrossEntropyLoss(weight=class_weights)
            lr = self.params.get('learning_rate', 5e-4)
            optimizer = optim.AdamW(model.parameters(), lr=lr,
                                   weight_decay=self.params.get('weight_decay', 1e-5))

            epochs = self.params.get('epochs', 100)
            patience = self.params.get('patience', 10)
            clip_grad_norm = self.params.get('clip_grad_norm', 1.0)

            # 定义UI更新回调函数
            def ui_callback(epoch, train_loss, train_acc, val_acc, progress):
                metrics = {
                    'epoch': epoch + 1,
                    'train_loss': train_loss,
                    'train_acc': train_acc,
                    'val_acc': val_acc
                }
                self.update_metrics.emit(metrics)
                self.update_progress.emit(progress)

            # 直接使用Bi_LSTM中的train_model函数，传入回调函数
            # 我们已经修改了Bi_LSTM.py中的train_model函数，使用验证集进行早停
            history, best_val_f1, best_epoch, test_metrics = train_model(
                model, train_loader, val_loader, test_loader, criterion, optimizer,
                device, epochs=epochs, patience=patience, clip_grad_norm=clip_grad_norm,
                callback=ui_callback  # 传入回调函数
            )

            # 加载最佳模型进行最终评估
            model.load_state_dict(torch.load("best_drilling_model.pth"))
            model.eval()

            true_labels = []
            pred_labels = []
            pred_probs = []

            with torch.no_grad():
                for inputs, labels in test_loader:
                    inputs = inputs.to(device).float()
                    outputs = model(inputs)
                    probs = torch.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs.data, 1)

                    true_labels.extend(labels.cpu().numpy())
                    pred_labels.extend(predicted.cpu().numpy())
                    pred_probs.extend(probs[:, 1].cpu().numpy())

            from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
            import numpy as np

            cm = confusion_matrix(true_labels, pred_labels)
            report = classification_report(true_labels, pred_labels, output_dict=True)

            fpr, tpr, _ = roc_curve(true_labels, pred_probs)
            roc_auc = auc(fpr, tpr)

            # 保存ROC曲线数据到CSV
            roc_data = pd.DataFrame({
                'fpr': fpr,
                'tpr': tpr,
                'auc': [roc_auc] * len(fpr)
            })
            roc_data.to_csv('roc_data.csv', index=False)

            # 不再执行交叉验证，使用三分法（训练集:验证集:测试集=6:2:2）进行数据划分，并使用验证集进行早停判断
            print("使用三分法数据划分，不执行交叉验证...")

            # 执行压力测试
            print("执行模型压力测试...")
            robustness_results = {}
            try:
                # 添加高斯噪声测试鲁棒性
                noise_levels = [0.05, 0.1, 0.15]
                noise_scores = []

                for noise_level in noise_levels:
                    # 创建带噪声的测试数据
                    X_test_noise = self.X_test + np.random.normal(0, noise_level, self.X_test.shape)
                    test_dataset_noise = DrillingDataset(X_test_noise, self.y_test)
                    test_loader_noise = DataLoader(test_dataset_noise, batch_size=batch_size, shuffle=False)

                    # 评估
                    model.eval()
                    correct = 0
                    total = 0
                    with torch.no_grad():
                        for inputs, labels in test_loader_noise:
                            inputs = inputs.to(device).float()
                            labels = labels.to(device).long()

                            outputs = model(inputs)
                            _, predicted = torch.max(outputs.data, 1)

                            total += labels.size(0)
                            correct += (predicted == labels).sum().item()

                    noise_acc = correct / total
                    noise_scores.append(noise_acc)
                    print(f"噪声级别 {noise_level} 准确率: {noise_acc:.4f}")

                robustness_results['noise_levels'] = noise_levels
                robustness_results['scores'] = noise_scores
            except Exception as e:
                print(f"压力测试错误: {str(e)}")
                robustness_results['error'] = str(e)

            # 训练结束，发送最终结果
            results = {
                'history': history,
                'best_epoch': best_epoch,
                'best_val_f1': best_val_f1,
                'test_accuracy': test_metrics['accuracy'],
                'test_f1': test_metrics['f1'],
                'confusion_matrix': cm,
                'classification_report': report,
                'roc_auc': roc_auc,
                'fpr': fpr,
                'tpr': tpr,
                'model_path': "best_drilling_model.pth",
                # 添加压力测试结果
                'robustness_results': robustness_results
            }

            self.training_finished.emit(results)

        except Exception as e:
            import traceback
            error_msg = f"训练出错: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)

# 主应用类
class DrillingAppUi(QMainWindow):
    def __init__(self):
        super().__init__()

        # 加载优化后的UI文件
        uic.loadUi('drilling_app.ui', self)

        # 初始化数据存储变量
        self.df = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.used_features = []
        self.training_thread = None
        self.model = None
        self.training_results = None
        self.input_size = None
        self.pred_df = None  # 添加预测数据框属性
        self.prediction_threshold = 0.5  # 默认预测阈值

        # 设置预设路径
        self.data_path_edit.setText(r"C:\Users\<USER>\Desktop\毕设\模型\数据集")

        # 连接信号和槽
        self.setup_connections()

        # 初始化图表
        self.setup_charts()

    def setup_connections(self):
        # 绑定按钮事件
        self.browse_btn.clicked.connect(self.browse_data_folder)
        self.load_data_btn.clicked.connect(self.load_dataset)
        self.preprocess_btn.clicked.connect(self.preprocess_data)
        self.train_btn.clicked.connect(self.start_training)
        self.stop_btn.clicked.connect(self.stop_training)
        self.load_model_btn.clicked.connect(self.load_model)
        self.browse_pred_btn.clicked.connect(self.browse_prediction_file)
        self.load_pred_data_btn.clicked.connect(self.load_prediction_data)
        self.predict_btn.clicked.connect(self.run_prediction)
        self.save_pred_btn.clicked.connect(self.save_prediction_results)
        self.save_results_btn.clicked.connect(self.save_evaluation_results)

        # 图表尺寸控制
        self.chart_width_spin.valueChanged.connect(self.update_chart_size)
        self.chart_height_spin.valueChanged.connect(self.update_chart_size)
        self.reset_chart_size_btn.clicked.connect(self.reset_chart_size)

        # 添加特征重要性分析按钮
        try:
            from PyQt5.QtWidgets import QPushButton
            self.analyze_importance_btn = QPushButton("分析特征重要性")
            # 尝试不同的布局添加按钮
            if hasattr(self, 'validation_layout'):
                self.validation_layout.addWidget(self.analyze_importance_btn)
            elif hasattr(self, 'verticalLayout_8'):
                self.verticalLayout_8.addWidget(self.analyze_importance_btn)
            elif hasattr(self, 'verticalLayout_5'):
                self.verticalLayout_5.addWidget(self.analyze_importance_btn)
            self.analyze_importance_btn.clicked.connect(self.analyze_feature_importance)
            print("成功添加特征重要性分析按钮")
        except Exception as e:
            print(f"创建特征重要性按钮失败: {str(e)}")

    def setup_charts(self):
        # 创建训练进度图表
        self.training_canvas = MatplotlibCanvas(width=5, height=4, dpi=100)
        layout = QVBoxLayout()
        layout.addWidget(self.training_canvas)
        self.verticalLayout_7.addLayout(layout)

        # 创建混淆矩阵图表 - 增加尺寸以显示完整标签
        self.cm_canvas = MatplotlibCanvas(width=5, height=5, dpi=100)
        layout = QVBoxLayout(self.cm_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.cm_canvas)

        # 创建ROC曲线图表 - 增加尺寸以显示完整标签
        self.roc_canvas = MatplotlibCanvas(width=5, height=5, dpi=100)
        layout = QVBoxLayout(self.roc_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.roc_canvas)

        # 不再创建嵌入式特征重要性图表，改为点击按钮弹出单独窗口

        # 删除多余的验证结果文本区域

        # 创建预测结果图表
        self.prediction_canvas = MatplotlibCanvas(
            width=self.chart_width_spin.value(),
            height=self.chart_height_spin.value(),
            dpi=100
        )
        layout = QVBoxLayout(self.prediction_chart_placeholder)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.prediction_canvas)

        # 添加预测阈值控件
        from PyQt5.QtWidgets import QSlider, QDoubleSpinBox, QLabel, QSpinBox
        from PyQt5.QtCore import Qt

        # 在chart_toolbar_layout中添加阈值控件
        self.threshold_label = QLabel("预测阈值:")
        self.chart_toolbar_layout.addWidget(self.threshold_label)

        # 添加滑块
        self.threshold_slider = QSlider(Qt.Horizontal)
        self.threshold_slider.setMinimum(0)
        self.threshold_slider.setMaximum(100)
        self.threshold_slider.setValue(int(self.prediction_threshold * 100))
        self.threshold_slider.setTickPosition(QSlider.TicksBelow)
        self.threshold_slider.setTickInterval(10)
        self.chart_toolbar_layout.addWidget(self.threshold_slider)

        # 添加数值输入框
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setDecimals(2)
        self.threshold_spin.setMinimum(0.0)
        self.threshold_spin.setMaximum(1.0)
        self.threshold_spin.setSingleStep(0.01)
        self.threshold_spin.setValue(self.prediction_threshold)
        self.chart_toolbar_layout.addWidget(self.threshold_spin)

        # 添加平滑窗口大小控件
        self.chart_toolbar_layout.addSpacing(20)  # 添加间距

        self.smooth_label = QLabel("平滑窗口:")
        self.chart_toolbar_layout.addWidget(self.smooth_label)

        # 添加平滑窗口大小输入框
        self.smooth_window_size = QSpinBox()
        self.smooth_window_size.setMinimum(1)
        self.smooth_window_size.setMaximum(21)
        self.smooth_window_size.setSingleStep(2)  # 每次增加2，保持奇数
        self.smooth_window_size.setValue(5)  # 默认值为5
        self.chart_toolbar_layout.addWidget(self.smooth_window_size)

        # 添加应用平滑按钮
        from PyQt5.QtWidgets import QPushButton
        self.apply_smooth_btn = QPushButton("应用平滑")
        self.chart_toolbar_layout.addWidget(self.apply_smooth_btn)
        self.apply_smooth_btn.clicked.connect(self.apply_smoothing)

        # 连接信号和槽
        self.threshold_slider.valueChanged.connect(self.update_threshold_from_slider)
        self.threshold_spin.valueChanged.connect(self.update_threshold_from_spin)

    def update_chart_size(self):
        """更新预测图表尺寸"""
        width = self.chart_width_spin.value()
        height = self.chart_height_spin.value()

        # 创建新的图表
        new_canvas = MatplotlibCanvas(width=width, height=height, dpi=100)

        # 重新绘制图表
        if hasattr(self, 'prediction_results') and self.prediction_results is not None:
            self.draw_prediction_chart(new_canvas)

        # 更新布局
        layout = self.prediction_chart_placeholder.layout()
        old_canvas = self.prediction_canvas
        layout.replaceWidget(old_canvas, new_canvas)
        old_canvas.deleteLater()
        self.prediction_canvas = new_canvas

    def update_threshold_from_slider(self, value):
        """从滑块更新阈值"""
        threshold = value / 100.0
        # 更新数值输入框，但不触发其valueChanged信号
        self.threshold_spin.blockSignals(True)
        self.threshold_spin.setValue(threshold)
        self.threshold_spin.blockSignals(False)
        # 更新阈值并重绘图表
        self.update_prediction_threshold(threshold)

    def update_threshold_from_spin(self, value):
        """从数值输入框更新阈值"""
        # 更新滑块，但不触发其valueChanged信号
        self.threshold_slider.blockSignals(True)
        self.threshold_slider.setValue(int(value * 100))
        self.threshold_slider.blockSignals(False)
        # 更新阈值并重绘图表
        self.update_prediction_threshold(value)

    def analyze_feature_importance(self):
        """使用Captum分析特征重要性并在单独窗口中绘制图表，结合模型的特征权重"""
        if not CAPTUM_AVAILABLE:
            print("警告: Captum库不可用，无法进行特征重要性分析")
            self.show_feature_importance_from_file()
            return

        if self.model is None:
            print("警告: 模型未加载，无法进行特征重要性分析")
            self.show_feature_importance_from_file()
            return

        try:
            # 准备数据
            device = next(self.model.parameters()).device

            # 创建一个示例输入数据
            if hasattr(self, 'X_test') and self.X_test is not None and len(self.X_test) > 0:
                # 使用测试集的一个批次
                batch_size = min(64, len(self.X_test))
                inputs = torch.tensor(self.X_test[:batch_size], dtype=torch.float32).to(device)
            else:
                # 创建一个随机输入
                seq_length = 30  # 默认序列长度
                input_size = next(self.model.parameters()).shape[1] if hasattr(self.model, 'lstm') else len(self.used_features)
                inputs = torch.randn(10, seq_length, input_size).to(device)  # 创建10个样本以增强稳定性

            # 创建集成梯度对象
            ig = IntegratedGradients(self.model)

            # 计算特征重要性
            attributions, _ = ig.attribute(inputs, target=1, return_convergence_delta=True)

            # 将归因结果求平均，得到每个特征的重要性分数
            feature_importance = attributions.abs().mean(dim=(0, 1)).detach().cpu().numpy()

            # 获取特征名称
            if hasattr(self, 'used_features') and self.used_features:
                feature_names = self.used_features
            else:
                feature_names = [f'Feature {i+1}' for i in range(len(feature_importance))]

            # 获取模型中的特征权重
            if hasattr(self.model, 'feature_weights'):
                # 结合模型的特征权重调整特征重要性
                model_weights = self.model.feature_weights.detach().cpu().numpy()
                print("模型特征权重:")
                for i, (name, weight) in enumerate(zip(feature_names, model_weights)):
                    print(f"- {name}: {weight:.4f}")

                # 将特征重要性与模型权重结合
                # 使用加权平均，确保物理机制的重要性得到体现
                combined_importance = (feature_importance * 0.5) + (model_weights * 0.5)
                print("\n结合模型权重后的特征重要性:")
                for i, (name, imp) in enumerate(zip(feature_names, combined_importance)):
                    print(f"- {name}: {imp:.6f}")

                # 使用结合后的重要性
                feature_importance = combined_importance

            # 保存特征重要性到文件
            import json
            importance_result = dict(zip(feature_names, feature_importance.tolist()))
            with open('feature_importance.json', 'w') as f:
                json.dump({k: float(v) for k, v in importance_result.items()}, f, indent=4)
            print(f"特征重要性已保存到 'feature_importance.json'")

            # 保存特征权重
            if hasattr(self.model, 'feature_weights'):
                feature_weights = {
                    feature_names[i]: float(self.model.feature_weights[i].detach().cpu().numpy())
                    for i in range(len(feature_names))
                }
                with open('feature_weights.json', 'w') as f:
                    json.dump(feature_weights, f, indent=4)
                print(f"特征权重已保存到 'feature_weights.json'")

            # 创建单独的figure窗口显示特征重要性
            plt.figure(figsize=(10, 8))

            # 排序特征重要性
            indices = np.argsort(feature_importance)
            sorted_importances = feature_importance[indices]
            sorted_names = [feature_names[i] for i in indices]

            # 绘制水平条形图
            y_pos = np.arange(len(sorted_names))
            plt.barh(y_pos, sorted_importances, align='center')
            plt.yticks(y_pos, sorted_names)
            plt.gca().invert_yaxis()  # 最重要的特征在顶部
            plt.xlabel('特征重要性分数')
            plt.title('特征重要性排序')
            plt.tight_layout()

            # 显示图表
            plt.show(block=False)  # 非阻塞显示，不会阻止主程序继续运行

            return importance_result

        except Exception as e:
            import traceback
            error_msg = f"特征重要性分析失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            self.show_feature_importance_from_file()
            return None

    def show_feature_importance_from_file(self):
        """从文件中加载特征重要性并在单独窗口中显示"""
        try:
            import json
            import os

            # 检查特征重要性文件是否存在
            if not os.path.exists('feature_importance.json'):
                print("警告: 特征重要性文件不存在")
                return

            # 加载特征重要性
            with open('feature_importance.json', 'r') as f:
                importance_dict = json.load(f)

            # 排序特征重要性
            sorted_items = sorted(importance_dict.items(), key=lambda x: x[1])
            feature_names = [item[0] for item in sorted_items]
            feature_importance = [item[1] for item in sorted_items]

            # 创建单独的figure窗口显示特征重要性
            plt.figure(figsize=(10, 8))

            # 绘制水平条形图
            y_pos = np.arange(len(feature_names))
            plt.barh(y_pos, feature_importance, align='center')
            plt.yticks(y_pos, feature_names)
            plt.gca().invert_yaxis()  # 最重要的特征在顶部
            plt.xlabel('特征重要性分数')
            plt.title('特征重要性排序 (从文件加载)')
            plt.tight_layout()

            # 显示图表
            plt.show(block=False)  # 非阻塞显示，不会阻止主程序继续运行

            print(f"从文件加载特征重要性成功")

        except Exception as e:
            import traceback
            error_msg = f"从文件加载特征重要性失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

    def update_prediction_threshold(self, threshold):
        """更新预测阈值并重绘图表"""
        self.prediction_threshold = threshold

        # 如果有预测结果，重新计算二分类预测并更新图表
        if hasattr(self, 'prediction_results') and self.prediction_results is not None:
            # 检查是否有平滑后的结果
            has_smoothed_results = 'Smoothed_Probability' in self.prediction_results.columns and 'Raw_Prediction' in self.prediction_results.columns

            if has_smoothed_results:
                # 使用新阈值重新计算原始二分类预测
                self.prediction_results['Raw_Prediction'] = (self.prediction_results['Probability'] > self.prediction_threshold).astype(int)

                # 重新应用平滑
                self.apply_smoothing()
            else:
                # 使用新阈值重新计算二分类预测
                self.prediction_results['Prediction'] = (self.prediction_results['Probability'] > self.prediction_threshold).astype(int)

                # 如果有真实标签，重新计算评估指标
                if 'True_Label' in self.prediction_results.columns:
                    true_labels = self.prediction_results['True_Label']
                    predictions = self.prediction_results['Prediction']

                    accuracy = accuracy_score(true_labels, predictions)
                    precision = precision_score(true_labels, predictions, zero_division=0)
                    recall = recall_score(true_labels, predictions, zero_division=0)
                    f1 = f1_score(true_labels, predictions, zero_division=0)

                    metrics_text = f"预测结果指标 (阈值: {self.prediction_threshold:.2f}):\n"
                    metrics_text += f"- 准确率: {accuracy:.4f}\n"
                    metrics_text += f"- 精确率: {precision:.4f}\n"
                    metrics_text += f"- 召回率: {recall:.4f}\n"
                    metrics_text += f"- F1分数: {f1:.4f}\n"

                    # 更新指标显示
                    if hasattr(self, 'pred_results'):
                        self.pred_results.setText(metrics_text)
                    elif hasattr(self, 'pred_data_info'):
                        self.pred_data_info.setText(metrics_text)

                # 重绘图表
                self.draw_prediction_chart()

    def apply_smoothing(self):
        """应用平滑处理并更新图表"""
        if not hasattr(self, 'prediction_results') or self.prediction_results is None:
            QMessageBox.warning(self, "警告", "没有预测结果可供平滑!")
            return

        # 获取平滑窗口大小
        window_size = self.smooth_window_size.value()

        # 确保窗口大小是奇数
        if window_size % 2 == 0:
            window_size += 1
            self.smooth_window_size.setValue(window_size)

        # 定义平滑函数
        def smooth_predictions(probabilities, predictions, window_size=5, method='both'):
            """对预测结果进行平滑处理

            参数:
                probabilities: 模型输出的概率值列表
                predictions: 二分类预测结果列表
                window_size: 滑动窗口大小
                method: 平滑方法，可选 'probability'(概率平均), 'voting'(多数投票), 'both'(两种方法都用)

            返回:
                smoothed_probs: 平滑后的概率值
                smoothed_preds: 平滑后的预测结果
            """
            import numpy as np
            from scipy.ndimage import uniform_filter1d

            # 转换为numpy数组以便处理
            probs = np.array(probabilities)
            preds = np.array(predictions)

            # 初始化结果
            smoothed_probs = probs.copy()
            smoothed_preds = preds.copy()

            # 方法1: 概率平均平滑 - 对原始概率值进行滑动平均
            if method in ['probability', 'both']:
                # 使用uniform_filter1d进行滑动平均
                smoothed_probs = uniform_filter1d(probs, size=window_size, mode='nearest')
                # 根据平滑后的概率重新计算预测结果
                if method == 'probability':
                    smoothed_preds = (smoothed_probs > self.prediction_threshold).astype(int)

            # 方法2: 多数投票平滑 - 对二分类结果进行滑动窗口多数投票
            if method in ['voting', 'both']:
                # 对每个位置应用滑动窗口多数投票
                for i in range(len(preds)):
                    # 确定窗口范围
                    start = max(0, i - window_size // 2)
                    end = min(len(preds), i + window_size // 2 + 1)
                    # 获取窗口内的预测
                    window_preds = preds[start:end]
                    # 计算窗口内1的比例
                    ones_ratio = np.sum(window_preds) / len(window_preds)
                    # 如果1的比例超过阈值(默认0.5)，则将当前位置设为1
                    if ones_ratio >= 0.5:
                        smoothed_preds[i] = 1
                    else:
                        smoothed_preds[i] = 0

            return smoothed_probs, smoothed_preds

        # 获取原始数据
        if 'Raw_Prediction' in self.prediction_results.columns:
            # 如果已经有平滑结果，使用原始数据重新平滑
            probabilities = self.prediction_results['Probability']
            raw_predictions = self.prediction_results['Raw_Prediction']
        else:
            # 如果还没有平滑结果，使用当前数据
            probabilities = self.prediction_results['Probability']
            raw_predictions = (np.array(probabilities) > self.prediction_threshold).astype(int)
            # 添加原始预测列
            self.prediction_results['Raw_Prediction'] = raw_predictions

        # 应用平滑
        smooth_method = 'both'  # 默认使用两种平滑方法
        smoothed_probs, smoothed_preds = smooth_predictions(
            probabilities, raw_predictions,
            window_size=window_size,
            method=smooth_method
        )

        # 更新预测结果
        self.prediction_results['Smoothed_Probability'] = smoothed_probs
        self.prediction_results['Prediction'] = smoothed_preds

        # 更新状态栏
        self.statusBar().showMessage(f"应用平滑处理: 窗口大小={window_size}, 方法={smooth_method}")

        # 重绘图表
        self.draw_prediction_chart()

        # 如果有真实标签，重新计算指标
        if 'True_Label' in self.prediction_results.columns:
            true_labels = self.prediction_results['True_Label']
            predictions = self.prediction_results['Prediction']

            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            accuracy = accuracy_score(true_labels, predictions)
            precision = precision_score(true_labels, predictions, zero_division=0)
            recall = recall_score(true_labels, predictions, zero_division=0)
            f1 = f1_score(true_labels, predictions, zero_division=0)

            metrics_text = f"预测结果指标 (阈值: {self.prediction_threshold:.2f}, 平滑窗口: {window_size}):\n"
            metrics_text += f"- 准确率: {accuracy:.4f}\n"
            metrics_text += f"- 精确率: {precision:.4f}\n"
            metrics_text += f"- 召回率: {recall:.4f}\n"
            metrics_text += f"- F1分数: {f1:.4f}\n"

            # 更新指标显示
            if hasattr(self, 'pred_results'):
                self.pred_results.setText(metrics_text)
            elif hasattr(self, 'pred_data_info'):
                self.pred_data_info.setText(metrics_text)

            # 重绘图表
            self.draw_prediction_chart()

    def reset_chart_size(self):
        """重置图表尺寸到默认值"""
        self.chart_width_spin.setValue(8)
        self.chart_height_spin.setValue(6)

    def draw_prediction_chart(self, canvas=None):
        """绘制预测结果图表

        图表说明:
        - 蓝线(b-): 表示模型预测的溢流概率，取值范围0-1
        - 红线(r-): 表示模型的二分类预测结果，1表示预测为溢流，0表示预测为正常
        - 绿色虚线(g--): 表示真实标签(如果有)，1表示实际发生溢流，0表示正常

        坐标轴:
        - X轴: 样本索引，表示时间序列中的数据点
        - Y轴: 标签/概率值，取值范围0-1
        """
        # 添加字体配置
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # Windows系统字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        if not hasattr(self, 'prediction_results') or self.prediction_results is None:
            return

        if canvas is None:
            canvas = self.prediction_canvas

        # 清除当前图表
        canvas.fig.clear()
        canvas.axes = canvas.fig.add_subplot(111)

        # 检查是否有平滑后的结果
        has_smoothed_results = 'Smoothed_Probability' in self.prediction_results.columns and 'Raw_Prediction' in self.prediction_results.columns

        if has_smoothed_results:
            # 如果有平滑结果，只显示平滑后的概率和预测结果
            # 添加平滑后的模型预测概率
            canvas.axes.plot(
                self.prediction_results['Smoothed_Probability'],
                'b-', alpha=0.7,
                label='预测概率'  # 蓝线 - 表示平滑后的溢流概率
            )

            # 添加平滑后的预测结果
            canvas.axes.plot(
                self.prediction_results['Prediction'],
                'r-', linewidth=2,
                label='预测结果'  # 红线 - 表示平滑后的二分类结果
            )
        else:
            # 如果没有平滑结果，则使用原始绘图方式
            # 添加模型预测概率
            canvas.axes.plot(
                self.prediction_results['Probability'],
                'b-', alpha=0.7,
                label='预测概率'  # 蓝线 - 表示溢流概率(0-1之间的连续值)
            )

            # 添加预测结果
            canvas.axes.plot(
                self.prediction_results['Prediction'],
                'r-', linewidth=2,
                label='预测结果'  # 红线 - 表示模型预测的二分类结果(0或1)
            )

        # 如果有真实标签，也添加进去
        if 'True_Label' in self.prediction_results.columns:
            canvas.axes.plot(
                self.prediction_results['True_Label'],
                'g--', alpha=0.7, linewidth=2,
                label='真实标签'  # 绿色虚线 - 表示实际的标签(0或1)
            )

        # 添加阈值线
        canvas.axes.axhline(y=self.prediction_threshold, color='k', linestyle=':', alpha=0.5, label=f'阈值({self.prediction_threshold:.2f})')

        # 添加更详细的坐标轴标签说明
        canvas.axes.set_title("溢流预测结果")
        canvas.axes.set_xlabel("样本索引 (时间序列上的点)")
        canvas.axes.set_ylabel("标签/概率 (0=正常, 1=溢流)")
        canvas.axes.set_ylim(-0.1, 1.1)

        # 添加网格线使图表更易读
        canvas.axes.grid(True, linestyle='--', alpha=0.6)

        # 添加图例并调整位置
        canvas.axes.legend(loc='best')
        canvas.fig.tight_layout(pad=1.5)
        canvas.draw()

    def browse_data_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择数据集文件夹")
        if folder:
            self.data_path_edit.setText(folder)

    def load_dataset(self):
        try:
            dataset_dir = self.data_path_edit.toPlainText().strip()

            if not os.path.exists(dataset_dir):
                QMessageBox.warning(self, "错误", f"路径不存在: {dataset_dir}")
                return

            # 修改DATASET_DIR全局变量
            import Bi_LSTM
            Bi_LSTM.DATASET_DIR = dataset_dir

            # 加载数据 - 现在load_data()返回两个值：train_df和test_df
            self.train_df, self.test_df = load_data()

            # 检查是否有数据
            if (self.train_df is None or len(self.train_df) == 0) and (self.test_df is None or len(self.test_df) == 0):
                QMessageBox.warning(self, "警告", "未加载到有效数据！")
                return

            # 检查是否有分开的训练集和测试集
            self.has_separate_datasets = self.test_df is not None

            # 如果没有分开的数据集，则使用combined_df
            if not self.has_separate_datasets:
                self.df = self.train_df  # train_df实际上是combined_df
                self.train_df = None
                info_text = f"成功加载数据(未检测到train/test子文件夹):\n"
                info_text += f"- 数据大小: {self.df.shape[0]} 行 × {self.df.shape[1]} 列\n"
                info_text += f"- 溢流样本: {(self.df['Label']==1).sum()} 个 ({(self.df['Label']==1).sum()/len(self.df)*100:.2f}%)\n"
                info_text += f"- 正常样本: {(self.df['Label']==0).sum()} 个 ({(self.df['Label']==0).sum()/len(self.df)*100:.2f}%)\n"
            else:
                # 如果有分开的数据集，显示两个数据集的信息
                info_text = f"成功加载数据(检测到train/test子文件夹):\n"
                info_text += f"- 训练集大小: {self.train_df.shape[0]} 行 × {self.train_df.shape[1]} 列\n"
                info_text += f"- 训练集溢流样本: {(self.train_df['Label']==1).sum()} 个 ({(self.train_df['Label']==1).sum()/len(self.train_df)*100:.2f}%)\n"
                info_text += f"- 训练集正常样本: {(self.train_df['Label']==0).sum()} 个 ({(self.train_df['Label']==0).sum()/len(self.train_df)*100:.2f}%)\n"
                info_text += f"- 测试集大小: {self.test_df.shape[0]} 行 × {self.test_df.shape[1]} 列\n"
                info_text += f"- 测试集溢流样本: {(self.test_df['Label']==1).sum()} 个 ({(self.test_df['Label']==1).sum()/len(self.test_df)*100:.2f}%)\n"
                info_text += f"- 测试集正常样本: {(self.test_df['Label']==0).sum()} 个 ({(self.test_df['Label']==0).sum()/len(self.test_df)*100:.2f}%)\n"

                # 计算当前测试集比例
                total_samples = len(self.train_df) + len(self.test_df)
                current_test_ratio = len(self.test_df) / total_samples
                info_text += f"- 当前测试集比例: {current_test_ratio:.2f} (界面设置: {self.test_ratio.value():.2f})\n"

                # 检查测试集比例是否符合界面设置
                if current_test_ratio < self.test_ratio.value():
                    info_text += f"- 注意: 测试集比例低于界面设置值，预处理时将从训练集补充数据\n"
                elif current_test_ratio > self.test_ratio.value():
                    info_text += f"- 注意: 测试集比例高于界面设置值，但将保持原样\n"

            # 获取有效特征列 - 基本特征
            basic_features = ['FlowOutPercent', 'SPP', 'TG', 'Sum']
            if self.has_separate_datasets:
                valid_features_train = [col for col in basic_features if col in self.train_df.columns and self.train_df[col].abs().sum() > 0]
                valid_features_test = [col for col in basic_features if col in self.test_df.columns and self.test_df[col].abs().sum() > 0]
                valid_features = list(set(valid_features_train) & set(valid_features_test))  # 取交集
            else:
                valid_features = [col for col in basic_features if col in self.df.columns and self.df[col].abs().sum() > 0]

            # 衍生特征将在预处理阶段通过calculate_constraint_features函数生成

            info_text += f"- 有效特征: {', '.join(valid_features)}"

            self.data_info.setText(info_text)
            self.statusBar().showMessage("数据加载成功")

            # 启用预处理按钮
            self.preprocess_btn.setEnabled(True)

        except Exception as e:
            import traceback
            error_msg = f"数据加载失败: {str(e)}\n{traceback.format_exc()}"
            self.data_info.setText(error_msg)
            self.statusBar().showMessage("数据加载失败")
            QMessageBox.critical(self, "错误", f"数据加载失败: {str(e)}")

    def preprocess_data(self):
        try:
            # 检查是否有数据可用
            if self.has_separate_datasets:
                if self.train_df is None or len(self.train_df) == 0:
                    QMessageBox.warning(self, "警告", "训练集数据为空!")
                    return
                if self.test_df is None or len(self.test_df) == 0:
                    QMessageBox.warning(self, "警告", "测试集数据为空!")
                    return
            else:
                if self.df is None or len(self.df) == 0:
                    QMessageBox.warning(self, "警告", "请先加载数据!")
                    return

            # 获取参数 - 从UI控件中获取值
            seq_length = self.seq_length.value()
            test_ratio = self.test_ratio.value()
            handle_imbalance = self.augment_checkbox.isChecked()

            # 根据是否有分开的训练集和测试集，采用不同的处理逻辑
            if self.has_separate_datasets:
                # 处理分开的训练集和测试集
                print("处理分开的训练集和测试集...")

                # 检查测试集比例是否符合界面设置
                total_samples = len(self.train_df) + len(self.test_df)
                current_test_ratio = len(self.test_df) / total_samples

                # 如果测试集比例低于界面设置，从训练集补充数据
                if current_test_ratio < test_ratio:
                    print(f"测试集比例({current_test_ratio:.2f})低于界面设置({test_ratio:.2f})，从训练集补充数据...")

                    # 计算需要补充的样本数量
                    target_test_size = int(total_samples * test_ratio)
                    samples_to_add = target_test_size - len(self.test_df)

                    # 使用分层抽样从训练集抽取数据补充到测试集
                    from sklearn.model_selection import train_test_split

                    # 计算抽样比例
                    sample_ratio = samples_to_add / len(self.train_df)

                    # 分层抽样
                    train_remain, train_to_test = train_test_split(
                        self.train_df,
                        test_size=sample_ratio,
                        random_state=42,
                        stratify=self.train_df['Label']  # 确保抽样保持类别比例
                    )

                    print(f"从训练集抽取 {len(train_to_test)} 个样本补充到测试集")
                    print(f"抽取前训练集大小: {len(self.train_df)}, 测试集大小: {len(self.test_df)}")

                    # 更新训练集和测试集
                    self.train_df = train_remain
                    self.test_df = pd.concat([self.test_df, train_to_test], ignore_index=True)

                    print(f"抽取后训练集大小: {len(self.train_df)}, 测试集大小: {len(self.test_df)}")
                    print(f"新的测试集比例: {len(self.test_df)/total_samples:.2f}")

                # 分别处理训练集和测试集
                train_df_processed = self.process_dataset(self.train_df, "训练集")
                test_df_processed = self.process_dataset(self.test_df, "测试集")

                # 提取特征和标签
                train_features = train_df_processed[FINAL_FEATURES]
                train_labels = train_df_processed['Label']
                test_features = test_df_processed[FINAL_FEATURES]
                test_labels = test_df_processed['Label']

                # 标准化特征 - 只在训练集上拟合scaler
                from sklearn.preprocessing import StandardScaler
                self.scaler = StandardScaler()
                train_features_scaled = pd.DataFrame(
                    self.scaler.fit_transform(train_features),
                    columns=FINAL_FEATURES
                )

                # 使用训练集上拟合的scaler转换测试集
                test_features_scaled = pd.DataFrame(
                    self.scaler.transform(test_features),
                    columns=FINAL_FEATURES
                )

                # 保存scaler和特征列表
                self.save_scaler_and_features()

                # 生成序列数据
                self.X_train, self.y_train, self.used_features = create_sequences(
                    train_features_scaled, train_labels, sequence_length=seq_length
                )
                self.X_test, self.y_test, _ = create_sequences(
                    test_features_scaled, test_labels, sequence_length=seq_length
                )

                self.input_size = len(self.used_features)

                # 从训练集中划分出验证集 (训练集:验证集 = 75%:25%)
                val_ratio = 0.25  # 验证集在训练集中的比例
                val_split_idx = int(len(self.X_train) * (1 - val_ratio))

                # 按时间顺序划分，保持时间连续性
                self.X_val = self.X_train[val_split_idx:]
                self.y_val = self.y_train[val_split_idx:]
                self.X_train = self.X_train[:val_split_idx]
                self.y_train = self.y_train[:val_split_idx]

                print(f"从训练集划分出验证集 - 训练集: {len(self.X_train)}, 验证集: {len(self.X_val)}, 测试集: {len(self.X_test)}")

                # 计算最终的三分法比例
                total = len(self.X_train) + len(self.X_val) + len(self.X_test)
                train_ratio = len(self.X_train) / total
                val_ratio = len(self.X_val) / total
                test_ratio = len(self.X_test) / total
                print(f"最终三分法比例 - 训练集: {train_ratio:.2f}, 验证集: {val_ratio:.2f}, 测试集: {test_ratio:.2f}")

                # 如果需要，对训练集进行数据增强 (仅对训练集应用SMOTE，验证集和测试集保持原始分布)
                if handle_imbalance:
                    self.X_train, self.y_train = self.apply_smote(self.X_train, self.y_train)

                # 准备信息文本
                info_text = self.prepare_info_text(seq_length, handle_imbalance)

            else:
                # 处理单一数据集
                print("处理单一数据集...")

                # 处理数据集
                processed_df = self.process_dataset(self.df, "数据集")

                # 提取特征和标签
                df_features = processed_df[FINAL_FEATURES]
                df_labels = processed_df['Label']

                # 标准化特征
                from sklearn.preprocessing import StandardScaler
                self.scaler = StandardScaler()
                df_features_scaled = pd.DataFrame(
                    self.scaler.fit_transform(df_features),
                    columns=FINAL_FEATURES
                )

                # 保存scaler和特征列表
                self.save_scaler_and_features()

                # 生成序列数据
                self.X, self.y, self.used_features = create_sequences(
                    df_features_scaled, df_labels, sequence_length=seq_length
                )
                self.input_size = len(self.used_features)

                # 如果需要，进行数据增强
                if handle_imbalance:
                    self.X, self.y = self.apply_smote(self.X, self.y)

                # 实现三分法数据划分 (训练集：验证集：测试集 = 6:2:2)
                # 对于时间序列数据，严格按时间顺序划分，确保验证集和测试集的时间点晚于训练集
                test_size = test_ratio
                val_size = test_ratio  # 验证集和测试集比例相同

                # 首先划分出测试集
                test_split_idx = int(len(self.X) * (1 - test_size))
                X_temp, self.X_test = self.X[:test_split_idx], self.X[test_split_idx:]
                y_temp, self.y_test = self.y[:test_split_idx], self.y[test_split_idx:]

                # 然后从剩余数据中划分出验证集
                # 计算验证集在剩余数据中的比例
                val_ratio_in_temp = val_size / (1 - test_size)
                val_split_idx = int(len(X_temp) * (1 - val_ratio_in_temp))

                # 按顺序划分，保持时间连续性
                self.X_train, self.X_val = X_temp[:val_split_idx], X_temp[val_split_idx:]
                self.y_train, self.y_val = y_temp[:val_split_idx], y_temp[val_split_idx:]

                # 计算最终的三分法比例
                total = len(self.X)
                train_ratio = len(self.X_train) / total
                val_ratio = len(self.X_val) / total
                test_ratio_actual = len(self.X_test) / total

                print(f"使用三分法时间顺序划分数据集 (训练集：验证集：测试集 = {train_ratio:.2f}:{val_ratio:.2f}:{test_ratio_actual:.2f})")
                print(f"训练集: {len(self.X_train)}, 验证集: {len(self.X_val)}, 测试集: {len(self.X_test)}")

                # 准备信息文本
                info_text = self.prepare_info_text(seq_length, handle_imbalance)

            # 显示信息并启用训练按钮
            self.preprocess_info.setText(info_text)
            self.statusBar().showMessage("数据预处理完成，详细描述已保存到dataset_description_ui.txt")
            self.train_btn.setEnabled(True)

        except Exception as e:
            import traceback
            error_msg = f"数据预处理失败: {str(e)}\n{traceback.format_exc()}"
            self.preprocess_info.setText(error_msg)
            self.statusBar().showMessage("数据预处理失败")
            QMessageBox.critical(self, "错误", f"数据预处理失败: {str(e)}")

    def process_dataset(self, df, dataset_name):
        """处理单个数据集的通用函数"""
        print(f"处理{dataset_name}...")

        # 检查是否有NaN值，并填充
        if df.isna().any().any():
            df = df.fillna(0)
            print(f"警告: {dataset_name}中发现NaN值，使用0填充")

        # 确保基本特征列存在
        basic_features = ['FlowOutPercent', 'SPP', 'TG', 'Sum']
        for col in basic_features:
            if col not in df.columns:
                df[col] = 0
                print(f"警告：{dataset_name}中添加缺失的基本特征 {col} 并用0填充")

        # 计算约束特征
        print(f"计算{dataset_name}的物理约束相关特征...")
        df_with_constraints = calculate_constraint_features(df.copy())
        print(f"计算约束特征后{dataset_name}形状: {df_with_constraints.shape}")

        # 检查是否有NaN值，并填充
        if df_with_constraints.isna().any().any():
            df_with_constraints = df_with_constraints.fillna(0)
            print(f"警告: 计算约束特征后{dataset_name}中发现NaN值，使用0填充")

        # 确保所有FINAL_FEATURES都存在
        for col in FINAL_FEATURES:
            if col not in df_with_constraints.columns:
                df_with_constraints[col] = 0
                print(f"警告: {dataset_name}中最终特征 '{col}' 不存在，已添加并用0填充")

        # 检查是否有 NaN 或 Infinite 值
        features_df = df_with_constraints[FINAL_FEATURES]
        if features_df.isnull().values.any() or np.isinf(features_df.values).any():
            print(f"警告: {dataset_name}特征数据中包含 NaN 或 Infinite 值，进行填充")
            df_with_constraints[FINAL_FEATURES] = features_df.replace([np.inf, -np.inf], np.nan).fillna(0)

        return df_with_constraints

    def save_scaler_and_features(self):
        """保存标准化器和特征列表"""
        import json
        import joblib

        features_filename = "used_features.json"
        scaler_filename = "scaler.pkl"

        try:
            with open(features_filename, 'w') as f:
                json.dump(FINAL_FEATURES, f)
            joblib.dump(self.scaler, scaler_filename)
            print(f"保存 used_features.json 和 scaler.pkl 到当前工作目录")
        except Exception as e:
            import traceback
            error_msg = f"保存特征列表或Scaler失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

    def apply_smote(self, X, y):
        """应用SMOTE数据增强"""
        # 找出少数类和多数类样本索引
        minority_indices = np.where(y == 1)[0] if np.sum(y == 1) < np.sum(y == 0) else np.where(y == 0)[0]
        majority_indices = np.where(y == 0)[0] if np.sum(y == 1) < np.sum(y == 0) else np.where(y == 1)[0]

        # 确定少数类和多数类标签
        minority_label = 1 if np.sum(y == 1) < np.sum(y == 0) else 0
        majority_label = 0 if minority_label == 1 else 1

        print(f"多数类标签为 {majority_label}, 样本数: {len(majority_indices)}")
        print(f"少数类标签为 {minority_label}, 样本数: {len(minority_indices)}")
        print(f"原始数据形状: {X.shape}, 标签分布: {np.bincount(y)}")

        # 检查少数类样本数量是否足够进行SMOTE
        min_samples_needed = 6  # SMOTE默认k_neighbors=5，需要至少k+1个样本
        if len(minority_indices) >= min_samples_needed:
            try:
                # 将三维序列数据展平为二维进行SMOTE
                n_samples, seq_len, n_features = X.shape
                X_reshaped = X.reshape(n_samples, seq_len * n_features)

                # 创建SMOTE实例并应用
                smote = SMOTE(random_state=42)
                X_resampled, y_resampled = smote.fit_resample(X_reshaped, y)

                # 将结果转回三维形状
                X_final = X_resampled.reshape(-1, seq_len, n_features)
                y_final = y_resampled

                print(f"SMOTE后数据形状: {X_final.shape}, 标签分布: {np.bincount(y_final)}")

                return X_final, y_final
            except Exception as e:
                print(f"SMOTE增强失败: {str(e)}")
                return X, y
        else:
            print(f"少数类样本数量({len(minority_indices)})不足以进行SMOTE增强(需要至少{min_samples_needed}个)")
            return X, y

    def prepare_info_text(self, seq_length, handle_imbalance):
        """准备预处理信息文本，包含详细的数据集统计信息"""
        # 计算原始数据点数
        if self.has_separate_datasets:
            total_data_points = len(self.train_df) + len(self.test_df)
        else:
            total_data_points = len(self.df)

        # 计算各数据集的原始数据点数（考虑序列长度）
        train_data_points = len(self.X_train) + seq_length - 1
        val_data_points = len(self.X_val) + seq_length - 1
        test_data_points = len(self.X_test) + seq_length - 1

        # 计算SMOTE后的标签分布
        train_label_counts = np.bincount(self.y_train)

        # 生成详细的数据集描述
        detailed_info = f"原始数据总计：{total_data_points}个数据点。\n"
        detailed_info += f"训练集：{train_data_points}个数据点，生成{len(self.X_train)}个序列样本。其中溢流样本{np.sum(self.y_train == 1)}个 (占总训练序列{np.sum(self.y_train == 1)/len(self.y_train):.2%})。\n"
        detailed_info += f"验证集：{val_data_points}个数据点，生成{len(self.X_val)}个序列样本。其中溢流样本{np.sum(self.y_val == 1)}个 (占总验证序列{np.sum(self.y_val == 1)/len(self.y_val):.2%})。\n"
        detailed_info += f"测试集：{test_data_points}个数据点，生成{len(self.X_test)}个序列样本。其中溢流样本{np.sum(self.y_test == 1)}个 (占总测试序列{np.sum(self.y_test == 1)/len(self.y_test):.2%})。\n"

        # 如果进行了数据增强，添加SMOTE后的信息
        if handle_imbalance:
            detailed_info += f"经过SMOTE对训练集进行过采样处理后，训练集中的溢流样本数量增加到{train_label_counts[1]}个，与正常样本数量达到平衡，标签分布为 0:{train_label_counts[0]}个, 1:{train_label_counts[1]}个。验证集和测试集保持原始分布不变。\n"

        # 保存详细描述到文件
        try:
            with open('dataset_description_ui.txt', 'w', encoding='utf-8') as f:
                f.write(detailed_info)
            print("数据集描述已保存到 'dataset_description_ui.txt'")
        except Exception as e:
            print(f"保存数据集描述失败: {str(e)}")

        # 为UI界面准备简洁版信息
        info_text = f"数据预处理完成:\n"
        info_text += f"- 序列长度: {seq_length}\n"
        info_text += f"- 特征数量: {self.input_size}\n"
        info_text += f"- 使用特征: {', '.join(self.used_features)}\n"

        if self.has_separate_datasets:
            info_text += f"- 数据来源: 从train和test子文件夹分别加载\n"
        else:
            info_text += f"- 数据来源: 从单一数据集按时间顺序划分\n"

        # 计算三分法比例
        total_samples = len(self.X_train) + len(self.X_val) + len(self.X_test)
        train_ratio = len(self.X_train) / total_samples
        val_ratio = len(self.X_val) / total_samples
        test_ratio = len(self.X_test) / total_samples
        info_text += f"- 三分法比例: 训练集 {train_ratio:.2f} : 验证集 {val_ratio:.2f} : 测试集 {test_ratio:.2f}\n"

        # 添加训练集、验证集和测试集信息
        info_text += f"- 训练集: {len(self.X_train)} 序列, 正样本: {np.sum(self.y_train == 1)}, 比例: {np.sum(self.y_train == 1)/len(self.y_train):.2%}\n"
        info_text += f"- 验证集: {len(self.X_val)} 序列, 正样本: {np.sum(self.y_val == 1)}, 比例: {np.sum(self.y_val == 1)/len(self.y_val):.2%}\n"
        info_text += f"- 测试集: {len(self.X_test)} 序列, 正样本: {np.sum(self.y_test == 1)}, 比例: {np.sum(self.y_test == 1)/len(self.y_test):.2%}\n"

        # 如果进行了数据增强，添加相关信息
        if handle_imbalance:
            info_text += f"- 数据增强: 仅对训练集使用SMOTE增强，验证集和测试集保持原始分布\n"

        # 不添加提示信息，保持界面简洁

        return info_text

    def start_training(self):
        """开始训练模型"""
        try:
            if self.X_train is None or self.y_train is None:
                QMessageBox.warning(self, "警告", "请先进行数据预处理!")
                return

            # 获取训练参数
            params = {
                'learning_rate': self.learning_rate.value(),
                'batch_size': self.batch_size.value(),
                'epochs': self.epochs.value(),
                'patience': self.patience.value(),
                'weight_decay': self.weight_decay.value(),
                'class_weight_factor': self.class_weight.value(),
                'clip_grad_norm': 1.0,
            }

            # 创建并启动训练线程
            self.training_thread = TrainingThread(
                self.X_train, self.y_train,
                self.X_val, self.y_val,
                self.X_test, self.y_test,
                self.input_size, params
            )

            # 连接信号
            self.training_thread.update_progress.connect(self.update_progress)
            self.training_thread.update_metrics.connect(self.update_metrics)
            self.training_thread.training_finished.connect(self.training_completed)
            self.training_thread.error_occurred.connect(self.handle_training_error)

            # 启动线程
            self.training_thread.start()

            # 更新UI状态
            self.train_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("模型训练中...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"训练启动失败: {str(e)}")

    def stop_training(self):
        """停止训练过程"""
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.terminate()
            self.training_thread.wait()
            self.statusBar().showMessage("训练已手动停止")
            self.train_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

    def update_progress(self, progress):
        """更新训练进度条"""
        self.progress_bar.setValue(progress)

    def update_metrics(self, metrics):
        """更新训练指标，仅显示文本，不更新图表"""
        epoch = metrics['epoch']
        train_loss = metrics['train_loss']
        train_acc = metrics['train_acc']
        val_acc = metrics['val_acc']

        # 更新指标显示文本
        info_text = f"当前训练进度:\n"
        info_text += f"Epoch: {epoch}\n"
        info_text += f"训练集损失: {train_loss:.4f}\n"
        info_text += f"训练集准确率: {train_acc:.4f}\n"
        info_text += f"验证集准确率: {val_acc:.4f}"
        self.training_info.setText(info_text)

        # 存储历史数据，但不立即更新图表
        if not hasattr(self, 'train_history'):
            self.train_history = {
                'epochs': [],
                'train_loss': [],
                'train_acc': [],
                'val_acc': []
            }

        self.train_history['epochs'].append(epoch)
        self.train_history['train_loss'].append(train_loss)
        self.train_history['train_acc'].append(train_acc)
        self.train_history['val_acc'].append(val_acc)

    def training_completed(self, results):
        """训练完成后的处理，只在最后绘制一次训练历史图表"""
        self.training_results = results
        self.model_path = results['model_path']

        # 更新UI状态
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.save_results_btn.setEnabled(True)

        # 显示最终结果
        self.display_evaluation_results(results)

        # 只在训练完成时绘制训练历史图表
        self.draw_training_history_chart(results['history'])

        # 使用matplotlib直接生成大尺寸训练历史图表
        try:
            import matplotlib.pyplot as plt

            # 设置中文字体支持
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 优先使用微软雅黑
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            plt.rcParams['font.size'] = 14  # 增大默认字体大小

            # 创建两个子图，分别显示损失和准确率
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

            # 获取历史数据
            history = results['history']
            epochs = list(range(len(history['train_loss'])))
            train_loss = history['train_loss']
            train_acc = history['train_acc']
            val_acc = history['val_acc']  # 使用验证集准确率

            # 绘制训练损失曲线 - 左侧子图
            ax1.plot(epochs, train_loss, 'b-', linewidth=2.5, label='训练集损失')
            ax1.set_title('训练损失曲线', fontsize=18)
            ax1.set_xlabel('训练轮次 (Epoch)', fontsize=16)
            ax1.set_ylabel('损失值 (Loss)', fontsize=16)
            ax1.grid(True, linestyle='--', alpha=0.7)
            ax1.legend(fontsize=14)

            # 设置y轴范围，确保从0开始，便于观察
            y_max = max(train_loss) * 1.1
            ax1.set_ylim(0, y_max)

            # 绘制准确率曲线 - 右侧子图
            ax2.plot(epochs, train_acc, 'b-', linewidth=2.5, label='训练集准确率')
            ax2.plot(epochs, val_acc, 'r-', linewidth=2.5, label='验证集准确率')

            # 设置标题和标签
            ax2.set_title('准确率曲线', fontsize=18)
            ax2.set_xlabel('训练轮次 (Epoch)', fontsize=16)
            ax2.set_ylabel('准确率 (Accuracy)', fontsize=16)
            ax2.grid(True, linestyle='--', alpha=0.7)
            ax2.legend(fontsize=14)

            # 设置y轴范围，通常准确率从0.5开始更有意义
            y_min = min(min(train_acc), min(val_acc))
            y_min = max(0.5, y_min * 0.95)  # 下限不低于0.5
            y_max = max(max(train_acc), max(val_acc)) * 1.05
            y_max = min(1.0, y_max)  # 上限不超过1.0
            ax2.set_ylim(y_min, y_max)

            # 美化图表
            for ax in [ax1, ax2]:
                ax.spines['top'].set_visible(False)
                ax.spines['right'].set_visible(False)
                ax.tick_params(axis='both', which='major', labelsize=12)

                # 添加网格线
                ax.grid(True, linestyle='--', alpha=0.6)

                # 添加轻微的背景色，增强可读性
                ax.set_facecolor('#f8f9fa')

            # 在图表上添加一些关键信息
            if 'val_f1' in history:
                # 找出最佳F1分数及其对应的轮次
                best_f1 = max(history['val_f1'])
                best_epoch = history['val_f1'].index(best_f1)

                # 在准确率图上标注最佳F1分数点
                ax2.plot(best_epoch, history['val_acc'][best_epoch], 'ro', markersize=10)
                ax2.annotate(f'最佳F1: {best_f1:.4f}',
                            xy=(best_epoch, history['val_acc'][best_epoch]),
                            xytext=(best_epoch+1, history['val_acc'][best_epoch]+0.05),
                            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5),
                            fontsize=12)

            # 添加图表标题
            fig.suptitle('训练历史图表', fontsize=20, y=0.98)

            # 保存并显式关闭图形
            fig.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间
            fig.savefig('training_history_large.png', dpi=300, bbox_inches='tight')
            plt.close(fig)  # 明确关闭图形对象
            print("已生成大尺寸训练历史图表: training_history_large.png")

        except Exception as e:
            print(f"生成大尺寸训练历史图表失败: {e}")

        # 显示提示信息
        # 强制刷新画布
        self.training_canvas.draw()
        QMessageBox.information(self, "训练完成",
                              f"模型训练完成!\n最佳验证集F1分数: {results['best_val_f1']:.4f}\n测试集准确率: {results['test_accuracy']:.4f}\n测试集F1分数: {results['test_f1']:.4f}\n\n已生成训练历史图表: training_history_large.png")
        self.statusBar().showMessage(f"训练完成，模型已保存至 {self.model_path}")

    def draw_training_history_chart(self, history):
        """只在训练完成后绘制训练历史图表"""
        # 清除当前画布
        self.training_canvas.fig.clear()

        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']  # 优先使用微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        # 创建两个子图，分别显示损失和准确率
        ax1 = self.training_canvas.fig.add_subplot(121)  # 1行2列的第1个子图
        ax2 = self.training_canvas.fig.add_subplot(122)  # 1行2列的第2个子图

        # 获取历史数据
        epochs = list(range(len(history['train_loss'])))
        train_loss = history['train_loss']
        train_acc = history['train_acc']
        val_acc = history['val_acc']  # 使用验证集准确率

        # 确定标签文本
        val_label = '验证集准确率'

        # 绘制训练损失曲线 - 左侧子图
        ax1.plot(epochs, train_loss, 'b-', linewidth=2, label='训练集损失')
        ax1.set_title('Training Loss', fontsize=14)
        ax1.set_xlabel('Epoch', fontsize=12)
        ax1.set_ylabel('Loss', fontsize=12)
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.legend(fontsize=10)

        # 设置y轴范围，确保从0开始，便于观察
        y_max = max(train_loss) * 1.1
        ax1.set_ylim(0, y_max)

        # 绘制准确率曲线 - 右侧子图
        ax2.plot(epochs, train_acc, 'b-', linewidth=2, label='训练集准确率')
        ax2.plot(epochs, val_acc, 'orange', linewidth=2, label=val_label)

        # 设置标题和标签
        ax2.set_title('Accuracy', fontsize=14)
        ax2.set_xlabel('Epoch', fontsize=12)
        ax2.set_ylabel('Accuracy', fontsize=12)
        ax2.grid(True, linestyle='--', alpha=0.7)
        ax2.legend(fontsize=10)

        # 设置y轴范围，通常准确率从0.5开始更有意义
        y_min = min(min(train_acc), min(val_acc))
        y_min = max(0.5, y_min * 0.95)  # 下限不低于0.5
        y_max = max(max(train_acc), max(val_acc)) * 1.05
        y_max = min(1.0, y_max)  # 上限不超过1.0
        ax2.set_ylim(y_min, y_max)

        # 美化图表
        for ax in [ax1, ax2]:
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.tick_params(axis='both', which='major', labelsize=10)

        # 更新画布
        self.training_canvas.fig.tight_layout(pad=2.0)
        self.training_canvas.draw()

        # 保存图表到文件
        self.training_canvas.fig.savefig('training_history.png', dpi=300, bbox_inches='tight')
        print(f"训练历史已保存为 'training_history.png'")

    def handle_training_error(self, error_msg):
        """处理训练错误"""
        self.training_info.setText(f"训练错误:\n{error_msg}")
        self.train_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.statusBar().showMessage("训练失败")
        QMessageBox.critical(self, "训练错误", error_msg)

    def display_evaluation_results(self, results):
        """显示评估结果"""
        # 添加字体配置
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # Windows系统字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        plt.rcParams['font.size'] = 12  # 增大字体大小

        # 显示混淆矩阵
        cm = results['confusion_matrix']
        self.cm_canvas.axes.clear()
        self.cm_canvas.fig.subplots_adjust(left=0.15, right=0.95, bottom=0.15, top=0.9)  # 调整边距

        if sns is not None:
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                      xticklabels=['正常', '溢流'],
                      yticklabels=['正常', '溢流'],
                      ax=self.cm_canvas.axes)
        else:
            # 使用matplotlib绘制简单热图
            self.cm_canvas.axes.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
            self.cm_canvas.axes.set_title("混淆矩阵", fontsize=14)
            self.cm_canvas.axes.set_xticks([0, 1])
            self.cm_canvas.axes.set_yticks([0, 1])
            self.cm_canvas.axes.set_xticklabels(['正常', '溢流'], fontsize=12)
            self.cm_canvas.axes.set_yticklabels(['正常', '溢流'], fontsize=12)

            # 在混淆矩阵中添加数字标注
            thresh = cm.max() / 2.
            for i in range(cm.shape[0]):
                for j in range(cm.shape[1]):
                    self.cm_canvas.axes.text(j, i, f"{cm[i, j]}",
                               ha="center", va="center", fontsize=14,
                               color="white" if cm[i, j] > thresh else "black")

        self.cm_canvas.axes.set_xlabel('预测标签', fontsize=13, labelpad=10)  # 增加labelpad以增加与图表的距离
        self.cm_canvas.axes.set_ylabel('真实标签', fontsize=13, labelpad=10)
        self.cm_canvas.axes.set_title('混淆矩阵', fontsize=14, pad=10)  # 增加pad以增加与图表的距离
        self.cm_canvas.fig.tight_layout(pad=2.0)  # 增加pad值
        self.cm_canvas.draw()

        # 显示ROC曲线
        self.roc_canvas.axes.clear()
        self.roc_canvas.fig.subplots_adjust(left=0.15, right=0.95, bottom=0.15, top=0.9)  # 调整边距

        self.roc_canvas.axes.plot(results['fpr'], results['tpr'],
                               'b-', label=f'AUC = {results["roc_auc"]:.4f}', linewidth=2)
        self.roc_canvas.axes.plot([0, 1], [0, 1], 'r--', linewidth=1.5)
        self.roc_canvas.axes.set_xlim([0, 1])
        self.roc_canvas.axes.set_ylim([0, 1])
        self.roc_canvas.axes.set_xlabel('假阳性率 (FPR)', fontsize=13, labelpad=10)
        self.roc_canvas.axes.set_ylabel('真阳性率 (TPR)', fontsize=13, labelpad=10)
        self.roc_canvas.axes.set_title('ROC曲线', fontsize=14, pad=10)
        self.roc_canvas.axes.legend(loc='lower right', fontsize=12)
        self.roc_canvas.axes.tick_params(axis='both', which='major', labelsize=11)  # 调整刻度标签大小
        self.roc_canvas.fig.tight_layout(pad=2.0)  # 增加pad值
        self.roc_canvas.draw()

        # 分析特征重要性并显示
        if self.model is not None:
            print("开始分析特征重要性...")
            self.analyze_feature_importance()
        else:
            print("模型未加载，尝试从文件加载特征重要性...")
            self.show_feature_importance_from_file()

        # 显示压力测试结果
        validation_text = ""

        # 添加压力测试结果
        if 'robustness_results' in results and results['robustness_results']:
            robustness_results = results['robustness_results']
            validation_text += "\n\n模型鲁棒性测试 (噪声测试):\n"

            if 'error' in robustness_results:
                validation_text += f"\n压力测试错误: {robustness_results['error']}\n"
            elif 'noise_levels' in robustness_results and 'scores' in robustness_results:
                validation_text += "\n不同噪声级别下的模型性能:\n"
                for i, (level, score) in enumerate(zip(robustness_results['noise_levels'], robustness_results['scores'])):
                    validation_text += f"  噪声级别 {level}: 准确率 {score:.4f}\n"

                # 计算性能下降百分比
                if len(robustness_results['scores']) > 0 and 'best_acc' in results:
                    baseline_acc = results['best_acc']
                    worst_acc = min(robustness_results['scores'])
                    degradation = (baseline_acc - worst_acc) / baseline_acc * 100
                    validation_text += f"\n最大性能下降: {degradation:.2f}%\n"

                # 生成抗噪性能测试图表
                try:
                    if 'noise_levels' in robustness_results and 'scores' in robustness_results:
                        noise_levels = robustness_results['noise_levels']
                        scores = robustness_results['scores']

                        # 尝试获取无噪声时的性能作为基线
                        if 'best_acc' in results:
                            # 将无噪声时的性能添加到图表中
                            noise_levels = [0] + noise_levels
                            scores = [results['best_acc']] + scores

                        # 创建图表
                        fig_robustness, ax_robustness = plt.subplots(figsize=(10, 6))

                        # 生成x轴标签
                        x_labels = [f"{level:.2f}" for level in noise_levels]
                        x_pos = np.arange(len(x_labels))

                        # 绘制柱状图
                        bars = ax_robustness.bar(x_pos, scores, align='center', alpha=0.7, color='skyblue')

                        # 在柱子上添加数值标注
                        for i, bar in enumerate(bars):
                            height = bar.get_height()
                            ax_robustness.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                                    f'{scores[i]:.4f}', ha='center', va='bottom', fontsize=10)

                        # 设置图表标签和标题
                        ax_robustness.set_xlabel('噪声级别', fontsize=12)
                        ax_robustness.set_ylabel('准确率', fontsize=12)
                        ax_robustness.set_title('模型在不同噪声级别下的性能', fontsize=14)
                        ax_robustness.set_xticks(x_pos)
                        ax_robustness.set_xticklabels(x_labels)
                        ax_robustness.set_ylim(0, 1.05)  # 设置y轴范围从0到1
                        ax_robustness.grid(True, linestyle='--', alpha=0.6)  # 添加网格线

                        # 保存图表
                        fig_robustness.tight_layout()
                        fig_robustness.savefig('robustness_test.png', dpi=300, bbox_inches='tight')
                        plt.close(fig_robustness)  # 关闭图形，释放内存

                        print(f"抗噪性能测试图表已保存为 'robustness_test.png'")
                        self.statusBar().showMessage(f"抗噪性能测试图表已保存为 'robustness_test.png'")
                except Exception as e:
                    print(f"生成抗噪性能测试图表失败: {str(e)}")

        # 可以将验证结果添加到控制台输出
        print(validation_text)

        # 显示分类报告
        report = results['classification_report']

        # 格式化文本显示
        report_text = f"分类报告:\n\n"
        report_text += f"              精确率    召回率    F1值      样本数\n"
        report_text += f"正常 (0)     {report['0']['precision']:.4f}    {report['0']['recall']:.4f}    {report['0']['f1-score']:.4f}    {report['0']['support']}\n"
        report_text += f"溢流 (1)     {report['1']['precision']:.4f}    {report['1']['recall']:.4f}    {report['1']['f1-score']:.4f}    {report['1']['support']}\n\n"
        report_text += f"准确率:      {report['accuracy']:.4f}\n"
        report_text += f"宏平均:      {report['macro avg']['precision']:.4f}    {report['macro avg']['recall']:.4f}    {report['macro avg']['f1-score']:.4f}    {report['macro avg']['support']}\n"
        report_text += f"加权平均:    {report['weighted avg']['precision']:.4f}    {report['weighted avg']['recall']:.4f}    {report['weighted avg']['f1-score']:.4f}    {report['weighted avg']['support']}\n"

        # 修正控件名
        self.metrics_text.setText(report_text)

    def save_evaluation_results(self):
        """保存评估结果"""
        if not self.training_results:
            QMessageBox.warning(self, "警告", "没有可保存的评估结果!")
            return

        try:
            # 选择保存路径
            save_path, _ = QFileDialog.getSaveFileName(self, "保存评估结果",
                                                     "evaluation_results",
                                                     "CSV文件 (*.csv);;所有文件 (*.*)")
            if not save_path:
                return

            # 确保有.csv后缀
            if not save_path.endswith('.csv'):
                save_path += '.csv'

            # 准备数据
            report = self.training_results['classification_report']
            cm = self.training_results['confusion_matrix']

            results_dict = {
                'model_path': [self.training_results['model_path']],
                'best_epoch': [self.training_results['best_epoch']],
                'best_accuracy': [self.training_results['best_acc']],
                'roc_auc': [self.training_results['roc_auc']],
                'accuracy': [report['accuracy']],
                'normal_precision': [report['0']['precision']],
                'normal_recall': [report['0']['recall']],
                'normal_f1': [report['0']['f1-score']],
                'normal_support': [report['0']['support']],
                'overflow_precision': [report['1']['precision']],
                'overflow_recall': [report['1']['recall']],
                'overflow_f1': [report['1']['f1-score']],
                'overflow_support': [report['1']['support']],
                'true_negative': [cm[0, 0]],
                'false_positive': [cm[0, 1]],
                'false_negative': [cm[1, 0]],
                'true_positive': [cm[1, 1]]
            }

            # 保存为CSV
            pd.DataFrame(results_dict).to_csv(save_path, index=False)

            QMessageBox.information(self, "成功", f"评估结果已保存至:\n{save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存评估结果失败: {str(e)}")

    def load_model(self):
        try:
            # 兼容控件和字符串
            if hasattr(self.model_path, "toPlainText"):
                model_path = self.model_path.toPlainText().strip()
            elif hasattr(self.model_path, "text"):
                model_path = self.model_path.text().strip()
            else:
                model_path = str(self.model_path).strip()

            # If the path is empty or doesn't exist, open file dialog
            if not model_path or not os.path.exists(model_path):
                model_path, _ = QFileDialog.getOpenFileName(
                    self, "选择模型文件", "",
                    "PyTorch模型 (*.pth);;所有文件 (*.*)"
                )

                if not model_path:  # User canceled
                    return

                # Update the text field with selected path
                self.model_path.setText(model_path)

            if not os.path.exists(model_path):
                QMessageBox.warning(self, "警告", f"模型文件不存在: {model_path}")
                return

            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

            # 加载模型状态字典，以便我们可以检查它的结构
            state_dict = torch.load(model_path, map_location=device)

            # 获取模型输入特征维度（从权重矩阵推断）
            # LSTM的输入权重矩阵包含特征维度信息
            lstm_weight_key = 'lstm.weight_ih_l0'
            if lstm_weight_key in state_dict:
                # 保存的模型输入特征数量
                saved_input_size = state_dict[lstm_weight_key].shape[1]
                print(f"检测到保存的模型输入特征维度: {saved_input_size}")

                # 如果当前设置的input_size与模型不同，则更新
                if not hasattr(self, 'input_size') or self.input_size != saved_input_size:
                    self.input_size = saved_input_size
                    print(f"已更新输入特征维度为: {self.input_size}")
            else:
                # 如果找不到权重键，则使用默认值或当前值
                if not hasattr(self, 'input_size') or self.input_size is None:
                    # 默认为4个特征 (常见的钻井特征数量)
                    self.input_size = 4
                    print(f"未能从模型中检测输入维度，使用默认值: {self.input_size}")

            # 使用正确的特征维度初始化模型
            self.model = DrillingModel(input_size=self.input_size).to(device)

            # 安全地加载模型权重
            try:
                self.model.load_state_dict(state_dict)
            except RuntimeError as e:
                # 如果直接加载失败，显示详细错误信息
                error_message = str(e)
                print(f"模型加载错误: {error_message}")

                # 提取错误信息中的维度不匹配细节
                import re
                shape_mismatch = re.findall(r'shape torch.Size\(\[(\d+), (\d+)\]\)', error_message)

                if shape_mismatch and len(shape_mismatch) >= 2:
                    # 第二个维度是特征数
                    saved_features = int(shape_mismatch[0][1])
                    current_features = int(shape_mismatch[1][1])

                    error_msg = (f"模型特征维度不匹配!\n"
                                f"保存的模型使用了 {saved_features} 个特征训练，"
                                f"但当前尝试加载到 {current_features} 个特征的模型。\n\n"
                                f"请确保使用与训练时相同数量的特征进行预测，或重新训练模型。")

                    QMessageBox.critical(self, "模型加载失败", error_msg)
                    return
                else:
                    # 其他类型的错误
                    QMessageBox.critical(self, "模型加载失败", f"加载模型时出错:\n{error_message}")
                    return

            # 设置为评估模式
            self.model.eval()

            # --- 加载标准化器 ---
            import joblib
            self.scaler = None  # 初始化scaler属性
            scaler_path = os.path.join(os.path.dirname(model_path), "scaler.pkl")
            if os.path.exists(scaler_path):
                try:
                    loaded_scaler = joblib.load(scaler_path)
                    self.scaler = loaded_scaler
                    print(f"标准化器已从 {scaler_path} 加载")

                    # 检查加载的scaler类型是否与StandardScaler匹配 (可选但推荐)
                    if not isinstance(loaded_scaler, StandardScaler):
                        print(f"警告: 加载的标准化器类型不匹配，期望StandardScaler，实际加载的是 {type(loaded_scaler).__name__}")
                        print("将尝试继续使用加载的标准化器，但可能导致预测结果不准确")
                except Exception as e:
                    error_msg = f"加载标准化器失败: {str(e)}"
                    print(error_msg)
                    QMessageBox.critical(self, "错误", f"加载标准化器失败\n{error_msg}\n\n请确保使用Bi_LSTM.py训练生成的模型和标准化器文件。")
                    self.scaler = None
                    self.predict_btn.setEnabled(False)  # 禁用预测按钮
                    return
            else:
                error_msg = f"标准化器文件不存在: {scaler_path}"
                print(f"警告: {error_msg}")
                QMessageBox.critical(self, "错误", f"标准化器文件不存在\n{error_msg}\n\n请确保使用Bi_LSTM.py训练生成的模型和标准化器文件。")
                self.scaler = None
                self.predict_btn.setEnabled(False)  # 禁用预测按钮
                return

            # --- 加载使用的特征列表 ---
            import json
            self.used_features_for_prediction = []  # 初始化特征列表属性
            features_path = os.path.join(os.path.dirname(model_path), "used_features.json")
            if os.path.exists(features_path):
                try:
                    with open(features_path, 'r') as f:
                        self.used_features_for_prediction = json.load(f)
                    print(f"使用的特征列表已从 {features_path} 加载: {self.used_features_for_prediction}")

                    # 检查加载的特征数量是否与模型输入维度匹配
                    if len(self.used_features_for_prediction) != self.input_size:
                        error_msg = f"加载的特征列表 ({len(self.used_features_for_prediction)}) 与模型输入维度 ({self.input_size}) 不匹配!"
                        print(f"警告: {error_msg}")
                        QMessageBox.critical(self, "错误", f"特征列表与模型不匹配\n{error_msg}\n\n请确保使用Bi_LSTM.py训练生成的模型和特征列表文件。")
                        self.predict_btn.setEnabled(False)  # 禁用预测按钮
                        return

                    # 检查加载的特征列表是否与全局定义的 FINAL_FEATURES 匹配
                    # 这个检查不是必需的，因为我们应该使用加载的特征列表，而不是全局定义的
                    # 但这里添加一个警告，如果它们不匹配
                    if set(self.used_features_for_prediction) != set(FINAL_FEATURES):
                        print(f"警告: 加载的特征列表与全局定义的 FINAL_FEATURES 不完全匹配")
                        print(f"- 加载的特征: {self.used_features_for_prediction}")
                        print(f"- 全局定义的特征: {FINAL_FEATURES}")
                        print("将使用加载的特征列表进行预测，而不是全局定义的特征列表")
                except Exception as e:
                    error_msg = f"加载特征列表失败: {str(e)}"
                    print(error_msg)
                    QMessageBox.critical(self, "错误", f"加载特征列表失败\n{error_msg}\n\n请确保使用Bi_LSTM.py训练生成的模型和特征列表文件。")
                    self.used_features_for_prediction = []
                    self.predict_btn.setEnabled(False)  # 禁用预测按钮
                    return
            else:
                error_msg = f"特征列表文件不存在: {features_path}"
                print(f"警告: {error_msg}")
                QMessageBox.critical(self, "错误", f"特征列表文件不存在\n{error_msg}\n\n请确保使用Bi_LSTM.py训练生成的模型和特征列表文件。")
                self.used_features_for_prediction = []
                self.predict_btn.setEnabled(False)  # 禁用预测按钮
                return

            # 启用预测按钮，因为我们成功加载了模型、标准化器和特征列表
            self.predict_btn.setEnabled(True)

            # 更新UI信息
            feature_info = f"模型已加载 (输入特征数: {self.input_size})\n"
            feature_info += f"预测将使用特征: {', '.join(self.used_features_for_prediction)}"
            if self.scaler is not None:
                feature_info += f"\n标准化器已加载: {type(self.scaler).__name__}"
            else:
                feature_info += "\n警告: 标准化器未加载，预测可能不准确!"

            QMessageBox.information(self, "成功", f"模型加载成功！\n模型路径: {model_path}\n{feature_info}")
            self.statusBar().showMessage(f"模型加载成功: {os.path.basename(model_path)} ({self.input_size}特征)")

            # 保存或更新用于推理的特征列表
            # 首选特征列表
            preferred_features = ['SPP', 'Sum', 'FlowOutPercent', 'TG']

            if not hasattr(self, 'used_features') or not self.used_features:
                # 如果没有预设的特征列表，尝试使用首选特征
                if self.input_size <= len(preferred_features):
                    # 如果模型的输入维度小于等于首选特征数量，使用首选特征的前几个
                    self.used_features = preferred_features[:self.input_size]
                    print(f"使用首选特征列表: {self.used_features}")
                else:
                    # 如果模型输入维度大于首选特征数量，添加其他可用特征
                    available_features = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
                    # 先添加所有首选特征
                    self.used_features = preferred_features.copy()
                    # 然后添加其他特征直到达到所需的特征数量
                    for feature in available_features:
                        if feature not in self.used_features and len(self.used_features) < self.input_size:
                            self.used_features.append(feature)
                    print(f"使用扩展特征列表: {self.used_features}")
            elif len(self.used_features) != self.input_size:
                # 如果预设特征列表与模型不匹配，尝试使用首选特征
                if self.input_size <= len(preferred_features):
                    # 如果模型的输入维度小于等于首选特征数量，使用首选特征的前几个
                    self.used_features = preferred_features[:self.input_size]
                    print(f"调整为首选特征列表: {self.used_features}")
                else:
                    # 如果模型输入维度大于首选特征数量，添加其他可用特征
                    available_features = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
                    # 先添加所有首选特征
                    self.used_features = preferred_features.copy()
                    # 然后添加其他特征直到达到所需的特征数量
                    for feature in available_features:
                        if feature not in self.used_features and len(self.used_features) < self.input_size:
                            self.used_features.append(feature)
                    print(f"调整为扩展特征列表: {self.used_features}")

        except Exception as e:
            error_msg = f"模型加载失败: {str(e)}\n{traceback.format_exc()}"
            QMessageBox.critical(self, "错误", error_msg)
            self.statusBar().showMessage("模型加载失败")

    def browse_prediction_file(self):
        """浏览预测数据文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择预测数据文件", "", "CSV文件 (*.csv);;Excel文件 (*.xls *.xlsx);;所有文件 (*.*)")
        if file_path:
            self.pred_data_path.setText(file_path)

    def load_prediction_data(self):
        try:
            file_path = self.pred_data_path.toPlainText().strip()
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                return

            # 初始化DataFrame和错误信息收集
            self.pred_df = None
            error_msgs = []

            methods = [
                # 方法1: 使用openpyxl读取 .xlsx 文件
                lambda: pd.read_excel(file_path, engine='openpyxl') if file_path.endswith('.xlsx') else None,

                # 方法2: 尝试使用xlrd读取 .xls 文件
                lambda: pd.read_excel(file_path, engine='xlrd') if file_path.endswith('.xls') else None,

                # 方法3: 不指定引擎，让pandas自动选择
                lambda: pd.read_excel(file_path),

                # 方法4: 使用特殊方法处理 .xls 文件
                lambda: self.handle_xls_file(file_path) if file_path.endswith('.xls') else None,

                # 方法5: 尝试读取CSV
                lambda: pd.read_csv(file_path) if file_path.endswith('.csv') else None
            ]

            # 依次尝试各种读取方法
            error_msgs = []
            for method_idx, method in enumerate(methods):
                try:
                    print(f"尝试方法 {method_idx + 1} 读取文件...")
                    result = method()
                    if result is not None and isinstance(result, pd.DataFrame) and len(result) > 0:
                        self.pred_df = result
                        print(f"方法 {method_idx + 1} 成功读取数据！")
                        break
                except Exception as e:
                    error_msg = str(e)
                    error_msgs.append(f"方法 {method_idx + 1} 读取失败: {error_msg}")
                    print(f"方法 {method_idx + 1} 失败: {error_msg}")
                    continue

            # 如果所有方法都失败
            if self.pred_df is None or len(self.pred_df) == 0:
                error_message = "无法读取文件数据!\n错误信息:\n" + "\n".join(error_msgs)
                QMessageBox.warning(self, "警告", error_message)
                return

            print(f"成功读取数据，原始形状: {self.pred_df.shape}，列名: {self.pred_df.columns.tolist()[:5]}...")

            # 检查原始数据中是否有标签列（不区分大小写）
            original_columns = self.pred_df.columns.tolist()
            print(f"原始列名: {original_columns}")

            # 检查是否有重复列名
            if len(original_columns) != len(set(original_columns)):
                print("警告: 发现重复列名，正在处理...")
                # 找出重复的列名
                duplicated_cols = [col for col in original_columns if original_columns.count(col) > 1]
                print(f"重复列名: {set(duplicated_cols)}")

                # 重命名重复列
                for col in set(duplicated_cols):
                    # 找出所有重复列的索引
                    indices = [i for i, x in enumerate(original_columns) if x == col]
                    # 保留第一个，重命名其他的
                    for j, idx in enumerate(indices[1:], 1):
                        new_col = f"{col}_{j}"
                        print(f"将重复列 {col} 重命名为 {new_col}")
                        # 创建新列
                        self.pred_df[new_col] = self.pred_df.iloc[:, idx]

                    # 更新列名列表
                    original_columns = self.pred_df.columns.tolist()

            # 检查是否有类似'label'的列（不区分大小写）
            label_columns = [col for col in original_columns if 'label' in col.lower() or '标签' in col.lower()]
            if label_columns:
                print(f"找到可能的标签列: {label_columns}")
                # 如果有多个可能的标签列，使用第一个
                if len(label_columns) > 1:
                    print(f"警告: 发现多个可能的标签列，使用第一个: {label_columns[0]}")

                # 重命名为标准的'Label'列
                label_col = label_columns[0]
                if label_col != 'Label':
                    self.pred_df['Label'] = self.pred_df[label_col].copy()
                    print(f"已将 {label_col} 复制为 Label")

                # 确保标签为整数类型
                try:
                    # 打印标签列的唯一值，帮助调试
                    unique_values = self.pred_df['Label'].unique()
                    print(f"标签列唯一值: {unique_values}")

                    # 尝试转换为整数
                    self.pred_df['Label'] = self.pred_df['Label'].astype(int)
                    print("标签已转换为整数类型")
                except Exception as e:
                    print(f"标签转换为整数失败: {str(e)}，尝试其他方法")
                    # 尝试替代方法处理标签
                    try:
                        # 如果标签是字符串形式的数字，先转换为浮点数再转为整数
                        self.pred_df['Label'] = self.pred_df['Label'].astype(float).astype(int)
                        print("标签已通过浮点数转换为整数")
                    except:
                        # 如果是文本标签，尝试映射
                        label_map = {'正常': 0, '溢流': 1, 'normal': 0, 'overflow': 1}
                        self.pred_df['Label'] = self.pred_df['Label'].map(label_map).fillna(0).astype(int)
                        print("标签已通过映射转换为整数")

            # 预处理数据
            self.pred_df = unify_columns(self.pred_df)

            # 保存预处理前的标签分布
            if 'Label' in self.pred_df.columns:
                label_counts_before = self.pred_df['Label'].value_counts().to_dict()
                print(f"预处理前标签分布: {label_counts_before}")

            self.pred_df = preprocess_features(self.pred_df)

            # 确保必需列存在（在计算约束特征前）
            required_cols = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
            for col in required_cols:
                if col not in self.pred_df.columns:
                    self.pred_df[col] = 0 # 添加缺失列并填充0，避免后续计算出错
                    print(f"警告：添加缺失的必需列 {col} 并用0填充")
                # 同时检查 trend 列是否存在，为后续计算准备
                if f'{col}_trend' not in self.pred_df.columns:
                    self.pred_df[f'{col}_trend'] = 0

            # ** 调用 calculate_constraint_features 计算新特征 **
            print("计算预测数据的物理约束相关特征...")
            from Bi_LSTM import WINDOW_SIZE  # 导入窗口大小常量
            self.pred_df = calculate_constraint_features(self.pred_df, window_size=WINDOW_SIZE) # 使用与训练相同的窗口大小
            print(f"预测数据计算约束特征后形状: {self.pred_df.shape}")

            # 检查是否有NaN值，并填充 (在计算完所有特征后进行填充)
            if self.pred_df.isna().any().any():
                self.pred_df = self.pred_df.fillna(0)
                print("警告: 预测数据中发现NaN值，使用0填充")

            # 检查预处理后的标签分布
            if 'Label' in self.pred_df.columns:
                label_counts_after = self.pred_df['Label'].value_counts().to_dict()
                print(f"预处理后标签分布: {label_counts_after}")

                # 检查预处理是否改变了标签
                if 'label_counts_before' in locals() and label_counts_before != label_counts_after:
                    print("警告: 预处理改变了标签分布!")

            # 如果没有Label列，但需要进行预测，则添加默认的Label列
            if 'Label' not in self.pred_df.columns:
                self.pred_df['Label'] = 0  # 创建默认标签列，方便后续处理
                print("注意: 预测数据没有标签列，已添加默认标签")

            # 显示数据信息
            info_text = f"预测数据加载成功:\n"
            info_text += f"- 数据大小: {self.pred_df.shape[0]} 行 × {self.pred_df.shape[1]} 列\n"

            if 'Label' in self.pred_df.columns:
                info_text += f"- 预设标签分布:\n"
                info_text += f"  · 标签0 (正常): {(self.pred_df['Label']==0).sum()} 个 ({(self.pred_df['Label']==0).sum()/len(self.pred_df)*100:.2f}%)\n"
                info_text += f"  · 标签1 (溢流): {(self.pred_df['Label']==1).sum()} 个 ({(self.pred_df['Label']==1).sum()/len(self.pred_df)*100:.2f}%)\n"

            # 不再考虑工况影响

            # 检查是否包含必要的特征
            available_features = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
            valid_features = [col for col in available_features if col in self.pred_df.columns and self.pred_df[col].abs().sum() > 0]

            info_text += f"- 有效特征: {', '.join(valid_features)}"
            self.pred_data_info.setText(info_text)

            self.statusBar().showMessage("预测数据加载成功")

        except Exception as e:
            import traceback
            error_msg = f"加载预测数据失败: {str(e)}\n{traceback.format_exc()}"
            self.pred_data_info.setText(error_msg)
            QMessageBox.critical(self, "错误", f"加载预测数据失败: {str(e)}")
            self.statusBar().showMessage("预测数据加载失败")

    def handle_xls_file(self, filepath):
        """手动处理 .xls 文件，从Bi_LSTM.py中移植过来的函数"""
        try:
            # 尝试使用 xlrd 读取
            import xlrd
            print(f"当前使用的xlrd版本: {xlrd.__version__}")

            # 如果版本太高，需要替代方案
            if xlrd.__version__ >= "2.0.0":
                print("警告: xlrd 2.0及以上版本不支持.xls，尝试替代方案")

                # 替代方案1: 尝试使用第三方模块
                try:
                    import pyexcel_xls
                    data = pyexcel_xls.get_data(filepath)
                    sheet_name = list(data.keys())[0]
                    sheet_data = data[sheet_name]

                    # 从行数据中提取列名和数据
                    headers = sheet_data[0]
                    rows = sheet_data[1:]

                    return pd.DataFrame(rows, columns=headers)
                except ImportError:
                    print("pyexcel_xls 未安装，尝试其他方法...")
                except Exception as e:
                    print(f"pyexcel_xls读取失败: {str(e)}")

                # 替代方案2: 尝试将xls文件转换为xlsx (需要win32com)
                try:
                    import win32com.client
                    import tempfile

                    print("尝试使用Excel应用程序转换文件格式...")
                    excel = win32com.client.Dispatch("Excel.Application")
                    excel.Visible = False

                    # 创建临时文件名
                    temp_dir = tempfile.gettempdir()
                    temp_xlsx = os.path.join(temp_dir, "temp_converted.xlsx")

                    try:
                        wb = excel.Workbooks.Open(os.path.abspath(filepath))
                        wb.SaveAs(temp_xlsx, 51)  # 51是xlsx格式的代码
                        wb.Close()
                        excel.Quit()

                        # 读取转换后的文件
                        result_df = pd.read_excel(temp_xlsx, engine='openpyxl')

                        # 删除临时文件
                        try:
                            os.remove(temp_xlsx)
                        except:
                            pass

                        return result_df
                    except Exception as conv_err:
                        if 'excel' in locals():
                            excel.Quit()
                        print(f"转换过程中出错: {str(conv_err)}")
                        raise
                except ImportError:
                    print("win32com 未安装，尝试其他方法...")
                except Exception as e:
                    print(f"使用Excel转换失败: {str(e)}")

                # 替代方案3: 尝试读取同名CSV文件
                csv_path = filepath.replace('.xls', '.csv')
                if os.path.exists(csv_path):
                    print(f"尝试读取同名CSV文件: {csv_path}")
                    return pd.read_csv(csv_path)

                raise Exception("所有读取.xls文件的方法均失败")

            else:
                # xlrd 1.x 版本可以正常读取 .xls
                wb = xlrd.open_workbook(filepath)
                sheet = wb.sheet_by_index(0)

                # 从xlrd工作表构建DataFrame
                data = []
                for i in range(sheet.nrows):
                    data.append(sheet.row_values(i))

                # 提取列名和数据行
                headers = data[0]
                rows = data[1:]

                # 将可能的数值列转换为数值类型
                df = pd.DataFrame(rows, columns=headers)
                numeric_columns = ['SPP', 'Sum', 'FlowOutPercent', 'TG']
                for col in df.columns:
                    for pattern in numeric_columns:
                        if pattern.lower() in col.lower():
                            try:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                            except:
                                pass
                return df
        except Exception as e:
            print(f"手动处理.xls文件失败: {str(e)}")
            raise

    def run_prediction(self):
        """运行预测"""
        try:
            # 严格检查模型、标准化器和特征列表是否已加载
            if self.model is None:
                QMessageBox.warning(self, "警告", "请先加载模型!")
                return

            if not hasattr(self, 'scaler') or self.scaler is None:
                QMessageBox.warning(self, "警告", "标准化器未加载! 请先加载包含scaler.pkl的模型。")
                return

            if not hasattr(self, 'used_features_for_prediction') or not self.used_features_for_prediction:
                QMessageBox.warning(self, "警告", "特征列表未加载! 请先加载包含used_features.json的模型。")
                return

            if self.pred_df is None:
                QMessageBox.warning(self, "警告", "请先加载预测数据!")
                return

            # 准备数据
            seq_length = self.seq_length.value()

            # ** 预测数据准备：筛选、填充、标准化 **
            # 使用加载的模型对应的特征列表 self.used_features_for_prediction
            features_for_prediction = self.used_features_for_prediction
            print(f"预测将使用特征 (从加载的模型特征列表获取): {features_for_prediction}")
            print(f"预测数据原始列数 (包含所有加载和计算的特征): {len(self.pred_df.columns)}")

            # 确保预测数据包含模型期望的所有特征，并按正确顺序排列
            # 创建一个只包含这些特征的新 DataFrame
            pred_data_prepared = pd.DataFrame()
            for col in features_for_prediction:
                if col in self.pred_df.columns:
                    # 如果列存在，复制过来
                    pred_data_prepared[col] = self.pred_df[col]
                else:
                    # 如果列不存在，添加一个全0列
                    pred_data_prepared[col] = 0
                    print(f"警告: 预测数据缺少模型期望的特征 '{col}'，已用0填充。")

            # 检查是否有 NaN 或 Infinite 值，并填充
            if pred_data_prepared.isnull().values.any() or np.isinf(pred_data_prepared.values).any():
                print("警告: 预测特征数据中包含 NaN 或 Infinite 值，进行填充。")
                pred_data_prepared = pred_data_prepared.replace([np.inf, -np.inf], np.nan).fillna(0) # 将Inf替换为NaN再填充0

            print(f"用于标准化的预测数据特征列: {pred_data_prepared.columns.tolist()}")

            # ** 应用加载的 scaler 进行标准化 (transform而非fit_transform) **
            try:
                print(f"对预测数据应用加载的标准化器 ({type(self.scaler).__name__})...")
                # 注意：这里的 transform 必须应用于具有与 fit 时完全相同列名和顺序的 DataFrame
                # 我们前面已经确保了 pred_data_prepared 具有正确的列和顺序
                # 应用标准化器
                scaled_data = self.scaler.transform(pred_data_prepared)

                # 转换回DataFrame以保留列名
                pred_features_scaled = pd.DataFrame(
                    scaled_data,
                    columns=features_for_prediction
                )
                print("成功使用加载的标准化器对预测数据进行转换")
            except Exception as e:
                error_msg = f"使用标准化器转换预测数据失败: {str(e)}\n{traceback.format_exc()}"
                print(f"错误: {error_msg}")
                QMessageBox.critical(self, "错误", f"标准化预测数据失败\n{error_msg}\n\n可能的原因:\n1. 预测数据与训练数据在特征列上不完全匹配\n2. 加载的标准化器文件损坏或与模型不符")
                return

            # ** 生成序列数据 (使用已经标准化并筛选好的特征 DataFrame) **
            # create_sequences 函数需要传入特征DF和标签DF (如果存在)
            has_labels = 'Label' in self.pred_df.columns and np.any(self.pred_df['Label'] > 0)
            df_labels_pred = self.pred_df['Label'] if has_labels else pd.Series(np.zeros(len(self.pred_df))) # 使用原始 pred_df 的标签

            # create_sequences 内部会根据 seq_length 截取对应的标签
            X_pred, true_labels, used_features_from_seq = create_sequences(
                pred_features_scaled, df_labels_pred, sequence_length=seq_length # 传入标准化后的特征DF
            )
            print(f"生成的预测序列数据: 形状={X_pred.shape}, 标签形状={true_labels.shape}, 使用特征={used_features_from_seq}")

            # 检查生成的序列特征数量是否与模型输入维度匹配 (双重检查)
            if X_pred.shape[2] != self.input_size:
                error_msg = f"生成的预测序列特征数量 ({X_pred.shape[2]}) 与模型输入维度 ({self.input_size}) 不匹配!"
                print(f"错误: {error_msg}")
                QMessageBox.critical(self, "错误", f"特征维度不匹配\n{error_msg}\n\n请确保预测数据处理过程与训练一致。")
                return

            # 如果没有足够的序列用于预测
            if len(X_pred) == 0:
                QMessageBox.warning(self, "警告", f"预测数据点不足 ({len(pred_features_scaled)}) 以构建长度为 {seq_length} 的序列进行预测!")
                return

            # 更新使用的特征列表
            self.used_features = used_features_from_seq
            print(f"预测使用的特征列: {self.used_features}")



            # 检查是否有真实标签
            has_labels = 'Label' in self.pred_df.columns and np.any(self.pred_df['Label'] > 0)

            if not len(X_pred):
                QMessageBox.warning(self, "警告", "没有足够的数据构建序列!")
                return

            # 再次检查特征维度
            print(f"X_pred 形状: {X_pred.shape}, 期望特征维度: {self.input_size}")
            if X_pred.shape[2] != self.input_size:
                QMessageBox.warning(self, "警告", f"特征维度不匹配! 模型期望 {self.input_size} 个特征，但实际有 {X_pred.shape[2]} 个特征。")
                return

            # 进行预测
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

            self.model.eval()
            pred_dataset = DrillingDataset(X_pred, np.zeros(len(X_pred)))
            pred_loader = torch.utils.data.DataLoader(pred_dataset, batch_size=64, shuffle=False)

            all_probs = []
            all_preds = []

            with torch.no_grad():
                for inputs, _ in pred_loader:
                    inputs = inputs.to(device).float()
                    outputs = self.model(inputs)
                    probs = torch.softmax(outputs, dim=1)
                    _, predicted = torch.max(outputs.data, 1)

                    all_probs.extend(probs[:, 1].cpu().numpy())
                    all_preds.extend(predicted.cpu().numpy())

            # 保存原始预测结果
            raw_predictions = (np.array(all_probs) > self.prediction_threshold).astype(int)  # 使用当前阈值生成二分类预测

            # 定义平滑函数
            def smooth_predictions(probabilities, predictions, window_size=5, method='both'):
                """对预测结果进行平滑处理

                参数:
                    probabilities: 模型输出的概率值列表
                    predictions: 二分类预测结果列表
                    window_size: 滑动窗口大小
                    method: 平滑方法，可选 'probability'(概率平均), 'voting'(多数投票), 'both'(两种方法都用)

                返回:
                    smoothed_probs: 平滑后的概率值
                    smoothed_preds: 平滑后的预测结果
                """
                import numpy as np
                from scipy.ndimage import uniform_filter1d

                # 转换为numpy数组以便处理
                probs = np.array(probabilities)
                preds = np.array(predictions)

                # 初始化结果
                smoothed_probs = probs.copy()
                smoothed_preds = preds.copy()

                # 方法1: 概率平均平滑 - 对原始概率值进行滑动平均
                if method in ['probability', 'both']:
                    # 使用uniform_filter1d进行滑动平均
                    smoothed_probs = uniform_filter1d(probs, size=window_size, mode='nearest')
                    # 根据平滑后的概率重新计算预测结果
                    if method == 'probability':
                        smoothed_preds = (smoothed_probs > self.prediction_threshold).astype(int)

                # 方法2: 多数投票平滑 - 对二分类结果进行滑动窗口多数投票
                if method in ['voting', 'both']:
                    # 对每个位置应用滑动窗口多数投票
                    for i in range(len(preds)):
                        # 确定窗口范围
                        start = max(0, i - window_size // 2)
                        end = min(len(preds), i + window_size // 2 + 1)
                        # 获取窗口内的预测
                        window_preds = preds[start:end]
                        # 计算窗口内1的比例
                        ones_ratio = np.sum(window_preds) / len(window_preds)
                        # 如果1的比例超过阈值(默认0.5)，则将当前位置设为1
                        if ones_ratio >= 0.5:
                            smoothed_preds[i] = 1
                        else:
                            smoothed_preds[i] = 0

                return smoothed_probs, smoothed_preds

            # 应用平滑处理
            window_size = 5  # 默认窗口大小
            smooth_method = 'both'  # 默认使用两种平滑方法

            # 获取平滑窗口大小，如果UI中有相应控件
            if hasattr(self, 'smooth_window_size') and hasattr(self.smooth_window_size, 'value'):
                window_size = self.smooth_window_size.value()

            # 应用平滑处理
            smoothed_probs, smoothed_preds = smooth_predictions(
                all_probs, raw_predictions,
                window_size=window_size,
                method=smooth_method
            )

            print(f"应用后处理平滑: 窗口大小={window_size}, 方法={smooth_method}")

            # 保存预测结果，包括原始和平滑后的结果
            self.prediction_results = pd.DataFrame({
                'Index': range(len(all_probs)),
                'Probability': all_probs,  # 原始模型预测概率
                'Smoothed_Probability': smoothed_probs,  # 平滑后的概率
                'Raw_Prediction': raw_predictions,  # 原始二分类预测
                'Prediction': smoothed_preds  # 平滑后的二分类预测
            })

            if has_labels:
                self.prediction_results['True_Label'] = true_labels

                # 计算指标
                predictions = self.prediction_results['Prediction']
                accuracy = accuracy_score(true_labels, predictions)
                precision = precision_score(true_labels, predictions, zero_division=0)
                recall = recall_score(true_labels, predictions, zero_division=0)
                f1 = f1_score(true_labels, predictions, zero_division=0)

                metrics_text = f"预测结果指标 (阈值: {self.prediction_threshold:.2f}):\n"
                metrics_text += f"- 准确率: {accuracy:.4f}\n"
                metrics_text += f"- 精确率: {precision:.4f}\n"
                metrics_text += f"- 召回率: {recall:.4f}\n"
                metrics_text += f"- F1分数: {f1:.4f}\n"

                # 使用适当的控件名称，根据UI文件中的定义
                # 可能是pred_data_info或其他名称
                if hasattr(self, 'pred_results'):
                    self.pred_results.setText(metrics_text)
                elif hasattr(self, 'pred_data_info'):
                    self.pred_data_info.setText(metrics_text)
                else:
                    # 回退选项：尝试使用其他可能的控件
                    for attr_name in ['pred_metrics', 'prediction_results']:  # 移除prediction_info
                        if hasattr(self, attr_name):
                            getattr(self, attr_name).setText(metrics_text)
                            break
                    else:
                        # 如果找不到合适的控件，显示在状态栏
                        self.statusBar().showMessage(f"预测成功 - 准确率: {accuracy:.4f}")
            else:
                message = "预测完成。\n无真实标签，无法计算评估指标。"
                # 同样尝试不同的控件名称
                if hasattr(self, 'pred_results'):
                    self.pred_results.setText(message)
                elif hasattr(self, 'pred_data_info'):
                    self.pred_data_info.setText(message)
                else:
                    for attr_name in ['prediction_info', 'pred_metrics', 'prediction_results']:
                        if hasattr(self, attr_name):
                            getattr(self, attr_name).setText(message)
                            break
                    else:
                        self.statusBar().showMessage("预测成功 - 无评估指标")

            # 绘制预测图表
            self.draw_prediction_chart()

            # 启用保存按钮
            self.save_pred_btn.setEnabled(True)
            self.statusBar().showMessage("预测完成")

        except Exception as e:
            error_msg = f"预测失败: {str(e)}\n{traceback.format_exc()}"

            # 尝试不同的控件名称来显示错误信息
            displayed_error = False
            for attr_name in ['pred_results', 'pred_data_info', 'prediction_info', 'pred_metrics']:
                if hasattr(self, attr_name):
                    getattr(self, attr_name).setText(error_msg)
                    displayed_error = True
                    break

            if not displayed_error:
                # 如果没有找到合适的控件，至少在控制台和对话框中显示错误
                print(error_msg)

            QMessageBox.critical(self, "错误", f"预测失败: {str(e)}")

    def save_prediction_results(self):
        """保存预测结果"""
        if not hasattr(self, 'prediction_results') or self.prediction_results is None:
            QMessageBox.warning(self, "警告", "没有可保存的预测结果!")
            return

        try:
            # 选择保存路径
            save_path, _ = QFileDialog.getSaveFileName(self, "保存预测结果",
                                                     "prediction_results",
                                                     "CSV文件 (*.csv);;所有文件 (*.*)")
            if not save_path:
                return

            # 确保有.csv后缀
            if not save_path.endswith('.csv'):
                save_path += '.csv'

            # 保存为CSV
            self.prediction_results.to_csv(save_path, index=False)

            QMessageBox.information(self, "成功", f"预测结果已保存至:\n{save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存预测结果失败: {str(e)}")

if __name__ == "__main__":
    # 检查环境
    missing_packages, device_info = check_environment()
    if missing_packages:
        error_msg = "缺少必要的库:\n" + "\n".join(missing_packages)
        QMessageBox.critical(None, "环境检查失败", error_msg)
        sys.exit(1)

    # 初始化应用
    try:
        config = initialize_app()

        # 准备模型文件
        prepare_model_file()

        # 启动应用
        app = QApplication(sys.argv)
        window = DrillingAppUi()
        window.show()

        # 注册清理函数
        app.aboutToQuit.connect(cleanup_resources)

        # 运行应用
        sys.exit(app.exec_())

    except Exception as e:
        import traceback
        error_msg = f"应用程序错误:\n{str(e)}\n\n详细信息:\n{traceback.format_exc()}"
        logging.error(error_msg)
        QMessageBox.critical(None, "严重错误", error_msg)
        sys.exit(1)
